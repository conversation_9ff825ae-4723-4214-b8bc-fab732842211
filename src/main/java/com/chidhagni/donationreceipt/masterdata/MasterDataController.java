
package com.chidhagni.donationreceipt.masterdata;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@CrossOrigin
@RestController
@RequestMapping("/master-data")
@RequiredArgsConstructor
public class MasterDataController {


    private final MasterDataService masterDataService;

//
//    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
//    public ResponseEntity<MasterDataDTO> create(@RequestBody @Valid MasterDataDTO masterDataDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {
//        MasterDataDTO masterDataDTO1 = masterDataService.create(masterDataDTO, userPrincipal);
//        return ResponseEntity.created(
//                        UriComponentsBuilder
//                                .fromPath("/master-data")
//                                .buildAndExpand()
//                                .toUri()
//                )
//                .body(masterDataDTO1);
//    }
//
//    @PatchMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
//    public ResponseEntity<MasterDataDTO> update(@RequestBody @Valid MasterDataDTO masterDataDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {
//        return ResponseEntity.ok().body(masterDataService.updateMasterData(masterDataDTO, userPrincipal));
//    }
//
//    @DeleteMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
//    public void deleteMasterData(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
//        masterDataService.deleteMasterData(id, userPrincipal);
//    }
//
//    @PatchMapping(value = {"/activate/{id}"}, produces = APPLICATION_JSON_VALUE)
//    public void activateMasterData(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
//        masterDataService.activateMasterData(id, userPrincipal);
//    }
//
//    @GetMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
//    public ResponseEntity<MasterDataDTO> getMasterData(@PathVariable UUID id) throws JsonProcessingException {
//        return ResponseEntity.ok().body(masterDataService.getMasterDataById(id));
//    }
//
//
//    @PostMapping("/all")
//    public ResponseEntity<GetAllMasterDataResponse> getAll(@RequestBody(required = false) PaginationRequestMasterData paginationRequest) {
//        if (paginationRequest == null) {
//            paginationRequest = PaginationRequestMasterData.builder().page(1).pageSize(10).searchKeyword("").build();
//        }
//        return ResponseEntity.ok().body(masterDataService.getAll(paginationRequest));
//    }
}
