#Web Related Configurations
server.servlet.context-path=/pheart/
server.hostUrl=http://localhost:3000
spring.profiles.default=local


spring.profiles.active=local

//CORS configuration
cors.allowed-origins=http://localhost:3000/,http://localhost:3001/
cors.allowed-methods=GET,POST,PUT,DELETE,PATCH
cors.allow-credentials=true
cors.max-age=3600

key=334171803473-4qspot2cbd3n3fhsbihv8vqsevaj6rta.apps.googleusercontent.com
secret=HiULhTcnfjabc-kh5EGjBbBG
api.clientId=rzp_test_pGFNbEz46vxXsS
api.clientSecret=IQStUkZwWs5t6M5dIl04XgQg


spring.security.oauth2.client.registration.google.clientId=1073981864538-********************************.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}
spring.security.oauth2.client.registration.google.scope=email, profile
app.oauth2.authorizedRedirectUris=http://localhost:3000/houzer/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect


logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.oauth2.client=DEBUG


# Swagger/OpenAPI (Springdoc) Configuration
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger.html
springdoc.swagger-ui.url=/api-docs
springdoc.info.title=Houzer API
springdoc.info.description=Houzer application API documentation
springdoc.info.version=1.0.0

# DB Configurations
spring.datasource.url=****************************************
spring.datasource.username=houzer
spring.datasource.password=houzer
spring.datasource.driver-class-name=org.postgresql.Driver

# Logging level for various modules
logging.level.root=info
#logging.level.org.springframework.security=debug
#logging.level.org.jooq = debug
email.host=smtp.gmail.com
email.port=587

email.contactus-send.to=<EMAIL>
email-readiness-send.to=<EMAIL>
user-registration-send.to=<EMAIL>
jwt.secret=verylongchidhganisecretyouwillnotunderstanddsadsakdjsadjsadnczxczlkjsakndlsakndsajcncknsadjsalkdnsandksakjcsnaknsadkjsadnsadnsadlkjsknsadlnsakdnsandsjdksajdksadksjdksjdkjsadnsadjsadnsaksajdlksanlk


#8 hour in millis
accessToken.expiry=28800000
#24 hours in millis
refreshToken.expiry=86400000

#Professional Credentials
email.professionals.password=tudrwxwmwuicfupr
email.professionals=<EMAIL>

#Registration Credentials
email.registration=<EMAIL>
email.registration.password=kabvmidquyyzdrdk

#Societies Credentials
email.societies=<EMAIL>
email.societies.password=hezvdleznamimgzj

#Developers Credentials
email.developers=<EMAIL>
email.developers.password=caeeznabqonwmkxg

logging.file.path=/var/log/houzer_logs
logging.file.name=houzer_logs.log
logging.file.max-size=5MB
logging.file.max-history=5+

email.submit.enquiry.services.cc=<EMAIL>

##############DMS Configuration#############
# File storage provider: 'minio' or 'digitalocean'
filestore.provider=minio

# MinIO Configuration (default)
filestore.minio.bucket.name=houzer
filestore.minio.access.name=minio
filestore.minio.access.secret=minio123
filestore.minio.url=http://127.0.0.1:9000

# DigitalOcean Spaces Configuration
filestore.digitalocean.bucket.name=chidhagni-ph
filestore.digitalocean.access-key=ch-pure-heart-dev-access-key
filestore.digitalocean.secret-key=OMWmZgZdJEVCrNSDSLlTfCSFM+Oun1GImUUt7zSWMrY
filestore.digitalocean.endpoint=https://chidhagni-ph.blr1.digitaloceanspaces.com
filestore.digitalocean.region=blr1

# Legacy folder configuration (still used by some services)
filestore.bids.folder.name=bids
fileStore.lists.folder.name=listing
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
task.status.listname.id=c166e081-9b82-49ec-9a1a-0428e426e33b
services.listname.id=88cf3b1d-8548-41d3-9910-c2f92dcf2815
admin.role.type.id=7e63a16c-04da-4e8b-a13e-7e43e5c6d8d3
# Society User Category Id
society.user.category.id=9fb6e12c-8c82-4d03-a675-5d6a4e78a9c7


token=Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pKpfhwXgjSNRAJt2XozZI9_ezpj025J36UBObgQDf5k
apiUrl=https://live-mt-server.wati.io/321589

# Wati api Urls
templateMessageUrl=https://live-mt-server.wati.io/321589/api/v1/sendTemplateMessage?whatsappNumber=
sessionFileUrl=https://live-mt-server.wati.io/321589/api/v1/sendSessionFile/
templateMessagesUrl=https://live-mt-server.wati.io/321589/api/v1/sendTemplateMessages
sessionMessagesUrl=https://live-mt-server.wati.io/321589/api/v1/sendTemplateMessages/v1/sendTemplateMessages/


#RazorPay key and secret
rzp-client-Id=rzp_test_WydCsRrfzPdpt3
rzp-client-Secret=PletPOGTatjcDHxrWjJSxOOB

# LeadSnapshots task values
task.notification.title=Task Created
task.notification.titleContent=regarding Snapshots uploaded in LeadManagement.
task.notification.description=Review and address the details provided in the new lead snapshot.
task.notification.url=http://localhost:3000/tasks/

task.notification.content=A task has been created and assigned to you

#SP Lead Id
default.configuration.sp.lead.id=59a4443f-5e7c-47f3-9696-51605ea75916

#CHS Lead Id
default.configuration.chs.lead.id=0db89bf3-ea8c-4cac-b99d-24f23719062a

#Houzer Employees Company Name
houzer.employee.company.name=HOUZER

#Chidhagni Employees Company Name
chidhagni.employee.company.name=CHIDHAGNI

#Cron Configurations

#IndividualVerificationTask
chidhagni.cron.individualverificationtask.expiry.enabled=false
chidhagni.cron.individualverificationtask.expiry.cronProfile=*/10 * * * * *
chidhagni.cron.individualverificationtask.lockAtMostForTime=PT5M
chidhagni.cron.individualverificationtask.batchSize=100
chidhagni.cron.individualverificationtask.mode=FULL
chidhagni.cron.individualverificationtask.lockName=IndividualVerificationAuditTask
chidhagni.cron.individualverificationtask.windowSize=15


#IndividualAndOrganisationTask
chidhagni.cron.individualandorganisationtask.expiry.enabled=false
chidhagni.cron.individualandorganisationtask.expiry.cronProfile=0 */1 * * * *
chidhagni.cron.individualandorganisationtask.lockAtMostForTime=PT5M
chidhagni.cron.individualandorganisationtask.batchSize=100
chidhagni.cron.individualandorganisationtask.mode=FULL
chidhagni.cron.individualandorganisationtask.lockName=IndividualAndOrganisationTask
chidhagni.cron.individualandorganisationtask.windowSize=15

#IndividualPasswordResetAudit
chidhagni.cron.individualpasswordresettask.expiry.enabled=false
chidhagni.cron.individualpasswordresettask.expiry.cronProfile=*/1 * * * * *
chidhagni.cron.individualpasswordresettask.lockAtMostForTime=PT5M
chidhagni.cron.individualpasswordresettask.batchSize=5
chidhagni.cron.individualpasswordresettask.mode=FULL
chidhagni.cron.individualpasswordresettask.lockName=IndividualPasswordResetAuditTask
chidhagni.cron.individualpasswordresettask.windowSize=1


#IndividualReportingTask
chidhagni.cron.individualreportingtask.expiry.enabled=false
chidhagni.cron.individualreportingtask.expiry.cronProfile=*/1 * * * * *
chidhagni.cron.individualreportingtask.lockAtMostForTime=PT5M
chidhagni.cron.individualreportingtask.batchSize=100
chidhagni.cron.individualreportingtask.mode=FULL
chidhagni.cron.individualreportingtask.lockName=IndividualReportingTask
chidhagni.cron.individualreportingtask.windowSize=15


#IndividualLeadAssignment
chidhagni.cron.individualleadassignment.expiry.enabled=false
chidhagni.cron.individualleadassignment.expiry.cronProfile=*/1 * * * * *
chidhagni.cron.individualleadassignment.lockAtMostForTime=PT5M
chidhagni.cron.individualleadassignment.batchSize=100
chidhagni.cron.individualleadassignment.mode=FULL
chidhagni.cron.individualleadassignment.lockName=IndividualLeadAssignment
chidhagni.cron.individualleadassignment.windowSize=15


#IndividualRolesAudit
chidhagni.cron.individualrolestask.expiry.enabled=false
chidhagni.cron.individualrolestask.expiry.cronProfile=*/10 * * * * *
chidhagni.cron.individualrolestask.lockAtMostForTime=PT5M
chidhagni.cron.individualrolestask.lockName=IndividualRolesAudit
chidhagni.cron.individualrolestask.mode=FULL
chidhagni.cron.individualrolestask.batchSize=5
chidhagni.cron.individualrolestask.windowSize=15

#IndividualPermissionTask
chidhagni.cron.individualpermissiontask.expiry.enabled=false
chidhagni.cron.individualpermissiontask.expiry.cronProfile=*/1 * * * * *
chidhagni.cron.individualpermissiontask.lockAtMostForTime=PT5M
chidhagni.cron.individualpermissiontask.batchSize=100
chidhagni.cron.individualpermissiontask.mode=FULL
chidhagni.cron.individualpermissiontask.lockName=IndividualPermissionTask
chidhagni.cron.individualpermissiontask.windowSize=15

#ServiceRequisitions
chidhagni.cron.servicerequisitionstask.expiry.enabled=false
chidhagni.cron.servicerequisitionstask.expiry.cronProfile=*/2 * * * * *
chidhagni.cron.servicerequisitionstask.lockAtMostForTime=PT5M
chidhagni.cron.servicerequisitionstask.batchSize=100
chidhagni.cron.servicerequisitionstask.mode=FULL
chidhagni.cron.servicerequisitionstask.lockName=ServiceRequisitions
chidhagni.cron.servicerequisitionstask.windowSize=15

chidhagni.cron.spindividualinsertion.expiry.enabled=false
chidhagni.cron.spindividualinsertion.expiry.cronProfile=*/2 * * * * *
chidhagni.cron.spindividualinsertion.lockAtMostForTime=PT5M
chidhagni.cron.spindividualinsertion.batchSize=100
chidhagni.cron.spindividualinsertion.mode=FULL
chidhagni.cron.spindividualinsertion.lockName=IndividualSPTask
chidhagni.cron.spindividualinsertion.windowSize=15

service.provider.roles.id=3676706b-fc31-4b41-956c-3c4c758aa663
society.roles.id=ad60ce4e-f528-4722-82aa-8757baafce7a
super.admin.roles.id=107dc277-e80a-4d4c-9f0a-48bcbad5bea3
employee.roles.id= cafac5b3-77dd-4fcf-b890-6a2f63b9ee44

ht.aniket.individual.id=59a4443f-5e7c-47f3-9696-51605ea75916

status.listNameId = 35ccd57b-636b-4c27-967d-9e9e2d181bbf

#EmployeeEmails
houzer.first.employee.email  =<EMAIL>
houzer.second.employee.email =<EMAIL>
houzer.first.employee.name   =employeeone
houzer.second.employee.name  =employeetwo


#Assigning Aniket and Manav for work for chs,sp,work_orders,dri
sr.work.with.assigned.to=59a4443f-5e7c-47f3-9696-51605ea75916
sr.work.with.chs.assigned.to=e310b3d9-0457-4741-9c14-64cec2def592

#address type primary
sp.address.primary=a0823f0e-5a16-4a7e-a8a7-572d57cf07cf


roles.tenant.admin=c11e1f0a-cd55-41e6-92ec-8e7c41f72d67
donation.receipt.category=aa053b8f-29fd-4120-8fc1-4d8c9a757e70
donation.receipt.subcategory.8g=a5fa1861-accf-45b6-86ed-fae73991bd93
donation.receipt.subcategory.logo=8fbbe939-046e-4dab-90ee-613ae09f36cf
donation.receipt.recipient.org=5a4d79f1-9c67-4b4f-92b5-fbc6b0cc94f8
donation.receipt.recipient.individual=abc72613-a04c-4690-a28f-98f0efa7ab10

roles.tenant=f5c03c6e-3e4f-45f2-b06f-207a59f0e312
roles.finance.account=ccac9452-763f-4d62-81b3-8b4584de3b38
roles.data.entry-operator=de94d105-ea2b-4777-a4a9-b84b77c03ea1
roles.donor=6f1c3911-94a7-4c86-9444-8f1a5ac72bfc
donor.type.entity=a4a48b76-f25e-44bc-a81d-d5634f5b5b86

roles.superadmin=f4a9c1b2-8e74-4cd7-9a67-3c1f1e9e8f24

msg91.auth-key=446638AKLU50SZ67fcd7faP1
msg91.template-id=68185808d6fc05336870b012
msg91.sender-id=CHIDGN
send.otp.url: https://control.msg91.com/api/v5/otp
verify.otp.url: https://control.msg91.com/api/v5/otp/verify?mobile=%s&otp=%s
resend.otp.url: https://control.msg91.com/api/v5/otp/retry?mobile=%s&retrytype=%s


donation.receipt.pdf=donation_receipt_pdf_dynamic

aes.encryption.key=dGhpc2lzYXNlY3JldGtleTEyMzQ1Njc4OTAxMjM0NTY=

