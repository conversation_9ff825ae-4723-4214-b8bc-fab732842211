# Integration Test Configuration for TestContainers
# This file is used by integration tests that use TestContainers with PostgreSQL

# Database configuration will be overridden by TestContainers
# These are fallback values in case TestContainers fails
spring.datasource.url=***************************************
spring.datasource.username=test
spring.datasource.password=test
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Enable Liquibase for integration tests
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.yaml

# Logging
logging.level.com.chidhagni=DEBUG
logging.level.org.springframework=INFO
logging.level.org.testcontainers=DEBUG

# Test specific settings
spring.main.allow-bean-definition-overriding=true

# Email properties for testing
email.professionals=<EMAIL>
email.professionals.password=test-password
email.societies=<EMAIL>
email.societies.password=test-password
email.developers=<EMAIL>
email.developers.password=test-password
email.registration=<EMAIL>
email.registration.password=test-password
email.donations=<EMAIL>
email.donations.password=test-password
email.host=smtp.gmail.com
email.port=587

# Required properties for DonorImportExportService
listnames.id.states=f17a2e1c-c287-4380-8bcb-54d5f23f6cd0 