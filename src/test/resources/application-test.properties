
# Test Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Disable Liquibase for unit tests
spring.liquibase.enabled=false

# Logging
logging.level.com.chidhagni=DEBUG
logging.level.org.springframework=INFO

# Test specific settings
spring.main.allow-bean-definition-overriding=true 
=======
# Test Configuration for File Store Integration Tests

# Web Related Configurations
server.servlet.context-path=/test/
server.port=0

# Disable unnecessary features for testing
spring.liquibase.enabled=false
spring.jpa.hibernate.ddl-auto=none

# File Storage Configuration for Testing
# Default to MinIO for tests unless overridden
filestore.provider=${FILESTORE_PROVIDER:minio}

# MinIO Test Configuration
filestore.minio.bucket.name=${TEST_MINIO_BUCKET_NAME:test-bucket}
filestore.minio.access.name=${MINIO_ACCESS_KEY:minioadmin}
filestore.minio.access.secret=${MINIO_SECRET_KEY:minioadmin}
filestore.minio.url=${MINIO_URL:http://localhost:9000}

# DigitalOcean Spaces Test Configuration
# These should be set as environment variables when testing with DO Spaces
filestore.digitalocean.bucket.name=${TEST_DO_BUCKET_NAME:chidhagni-ph}
filestore.digitalocean.access-key=${DO_SPACES_ACCESS_KEY:test-access-key}
filestore.digitalocean.secret-key=${DO_SPACES_SECRET_KEY:test-secret-key}
filestore.digitalocean.endpoint=${DO_SPACES_ENDPOINT:https://test.digitaloceanspaces.com}
filestore.digitalocean.region=${DO_SPACES_REGION:nyc3}

# Multipart configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Logging configuration for tests
logging.level.com.chidhagni.filestore=DEBUG
logging.level.software.amazon.awssdk=WARN
logging.level.io.minio=WARN

# Disable security for tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

