package com.chidhagni.donationreceipt.donors;

import com.chidhagni.donationreceipt.common.exception.DuplicateDonorException;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonorDropdownResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.request.CommunicationRequestDto;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsDTO;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsPaginationRequest;
import com.chidhagni.donationreceipt.donors.dto.response.DonationResponses;
import com.chidhagni.donationreceipt.donors.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.donors.dto.response.GetAllDonors;
import com.chidhagni.donationreceipt.donors.utils.DonorMapper;
import com.chidhagni.donationreceipt.mobileotp.MobileOtpService;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.Aes256EncryptionUtils;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.EncryptedData;
import com.chidhagni.utils.HelperClassToSendCommunication;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.Condition;
import org.jooq.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DonorsService Unit Tests")
class DonorsServiceTest {

    @Mock
    private DonorsRepository donorsRepository;

    @Mock
    private DonorMapper donorMapper;

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private NotificationManager notificationManager;

    @Mock
    private WatiService watiService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private HelperClassToSendCommunication helperClassToSendCommunication;

    @Mock
    private MobileOtpService mobileOtpService;

    @Mock
    private Aes256EncryptionUtils encryptionUtils;

    @InjectMocks
    private DonorsService donorsService;

    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonorId;
    private DonorsDTO testDonorsDTO;
    private Donors testDonor;
    private DonorsResponse testDonorsResponse;
    private UserPrincipal testUserPrincipal;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonorId = UUID.randomUUID();

        testUserPrincipal = new UserPrincipal(testUserId);

        testDonorsDTO = DonorsDTO.builder()
                .tenantOrgId(testOrgId)
                .name("Test Donor")
                .email("<EMAIL>")
                .contactNumber("1234567890")
                .panNo("**********")
                .build();

        testDonor = new Donors();
        testDonor.setId(testDonorId);
        testDonor.setName("Test Donor");
        testDonor.setEmail("<EMAIL>");
        testDonor.setMobileNumber("1234567890");
        testDonor.setPanNo("**********");
        testDonor.setTenantOrgId(testOrgId);
        testDonor.setIsActive(true);
        testDonor.setCreatedBy(testUserId);
        testDonor.setCreatedOn(LocalDateTime.now());

        testDonorsResponse = DonorsResponse.builder()
                .id(testDonorId)
                .name("Test Donor")
                .email("<EMAIL>")
                .contactNumber("1234567890")
                .panNo("**********")
                .tenantOrgId(testOrgId)
                .isActive(true)
                .build();
    }

    @Test
    @DisplayName("Should create donor successfully")
    void createDonor_Success() {
        // Given
        when(donorsRepository.existsByTenantOrgIdAndEmailAndPanNo(testOrgId, "<EMAIL>", "**********"))
                .thenReturn(false);
        when(donorMapper.mapToDonors(testDonorsDTO, testUserPrincipal))
                .thenReturn(testDonor);
        when(donorsRepository.create(testDonor)).thenReturn(testDonor);

        // When
        UUID result = donorsService.create(testDonorsDTO, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(testDonorId, result);
        verify(donorsRepository).existsByTenantOrgIdAndEmailAndPanNo(testOrgId, "<EMAIL>", "**********");
        verify(donorMapper).mapToDonors(testDonorsDTO, testUserPrincipal);
        verify(donorsRepository).create(testDonor);
    }

    @Test
    @DisplayName("Should throw exception when donor already exists")
    void createDonor_AlreadyExists_ThrowsException() {
        // Given
        when(donorsRepository.existsByTenantOrgIdAndEmailAndPanNo(testOrgId, "<EMAIL>", "**********"))
                .thenReturn(true);

        // When & Then
        assertThrows(DuplicateDonorException.class, () -> {
            donorsService.create(testDonorsDTO, testUserPrincipal);
        });
        verify(donorsRepository).existsByTenantOrgIdAndEmailAndPanNo(testOrgId, "<EMAIL>", "**********");
        verify(donorsRepository, never()).create(any());
    }

    @Test
    @DisplayName("Should get donor by ID successfully")
    void getById_Success() {
        // Given
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);
        when(donorMapper.mapToDonorsResponse(testDonor))
                .thenReturn(testDonorsResponse);

        // When
        DonorsResponse result = donorsService.getById(testDonorId);

        // Then
        assertNotNull(result);
        assertEquals(testDonorId, result.getId());
        verify(donorsRepository).getByDonorId(testDonorId);
        verify(donorMapper).mapToDonorsResponse(testDonor);
    }

    @Test
    @DisplayName("Should throw exception when donor not found")
    void getById_NotFound_ThrowsException() {
        // Given
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            donorsService.getById(testDonorId);
        });
        verify(donorsRepository).getByDonorId(testDonorId);
        verify(donorMapper, never()).mapToDonorsResponse(any());
    }

    @Test
    @DisplayName("Should update donor successfully")
    void updateDonor_Success() {
        // Given
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);
        when(donorsRepository.existsByTenantOrgIdAndEmailAndPanNoExcludingId(testDonorId, testOrgId, "<EMAIL>", "**********"))
                .thenReturn(false);
        doNothing().when(donorsRepository).update(testDonor);
        when(donorMapper.mapToDonorsResponse(testDonor))
                .thenReturn(testDonorsResponse);
        when(encryptionUtils.encrypt(any())).thenReturn(new EncryptedData("encrypted", "nonce"));

        // When
        DonorsResponse result = donorsService.update(testDonorId, testDonorsDTO, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(testDonorId, result.getId());
        verify(donorsRepository).getByDonorId(testDonorId);
        verify(donorsRepository).existsByTenantOrgIdAndEmailAndPanNoExcludingId(testDonorId, testOrgId, "<EMAIL>", "**********");
        verify(donorsRepository).update(testDonor);
    }


    @Test
    @DisplayName("Should activate donor successfully")
    void activateDonor_Success() {
        // Given
        testDonor.setIsActive(false);
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donorsService.activateDonors(testDonorId, testUserId);

            // Then
            assertTrue(testDonor.getIsActive());
            assertEquals(testUserId, testDonor.getUpdatedBy());
            assertEquals(mockTime, testDonor.getUpdatedOn());
            verify(donorsRepository).getByDonorId(testDonorId);
            verify(donorsRepository).update(testDonor);
        }
    }

    @Test
    @DisplayName("Should deactivate donor successfully")
    void deactivateDonor_Success() {
        // Given
        testDonor.setIsActive(true);
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donorsService.deactivateDonors(testDonorId, testUserId);

            // Then
            assertFalse(testDonor.getIsActive());
            assertEquals(testUserId, testDonor.getUpdatedBy());
            assertEquals(mockTime, testDonor.getUpdatedOn());
            verify(donorsRepository).getByDonorId(testDonorId);
            verify(donorsRepository).update(testDonor);
        }
    }

    @Test
    @DisplayName("Should get donor dropdown successfully")
    void getDonorDropdown_Success() {
        // Given
        List<DonorDropdownResponse> mockDropdowns = Arrays.asList(
            DonorDropdownResponse.builder()
                .id(testDonorId)
                .name("Test Donor")
                .build()
        );
        when(donorsRepository.getDonorDropdown()).thenReturn(mockDropdowns);

        // When
        List<DonorDropdownResponse> result = donorsService.getDonorDropdown();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testDonorId, result.get(0).getId());
        verify(donorsRepository).getDonorDropdown();
    }

    @Test
    @DisplayName("Should get tenant donor dropdown successfully")
    void getTenantDonorDropdown_Success() {
        // Given
        List<DonorDropdownResponse> mockDropdowns = Arrays.asList(
            DonorDropdownResponse.builder()
                .id(testDonorId)
                .name("Test Donor")
                .build()
        );
        when(donorsRepository.getTenantDonorDropdown(testOrgId)).thenReturn(mockDropdowns);

        // When
        List<DonorDropdownResponse> result = donorsService.getTenantDonorDropdown(testOrgId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testDonorId, result.get(0).getId());
        verify(donorsRepository).getTenantDonorDropdown(testOrgId);
    }

    @Test
    @DisplayName("Should get all donations by donor ID successfully")
    void getAllDonationsByDonorId_Success() {
        // Given
        List<DonationResponses> mockDonations = Arrays.asList(
            DonationResponses.builder()
                .name("Test Donor")
                .email("<EMAIL>")
                .donationAmount("1000.0")
                .build()
        );
        when(donorsRepository.getAllDonationsByDonorId(testDonorId)).thenReturn(mockDonations);

        // When
        List<DonationResponses> result = donorsService.getAllDonationsByDonorId(testDonorId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(donorsRepository).getAllDonationsByDonorId(testDonorId);
    }
} 