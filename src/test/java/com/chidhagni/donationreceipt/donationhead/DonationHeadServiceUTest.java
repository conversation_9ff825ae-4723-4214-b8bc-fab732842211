package com.chidhagni.donationreceipt.donationhead;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadPaginationRequest;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadRequestDTO;
import com.chidhagni.donationreceipt.donationhead.dto.request.GetAllDonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsByOrgIdResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsDropDown;
import com.chidhagni.donationreceipt.donationhead.utils.DonationHeadMapper;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.utils.DateUtils;
import org.jooq.Condition;
import org.jooq.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for DonationHeadService
 * Tests business logic in isolation with mocked dependencies
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DonationHeadService Unit Tests")
class DonationHeadServiceUTest {

    @Mock
    private DonationHeadRepository donationHeadRepository;

    @Mock
    private DonationHeadMapper donationHeadMapper;

    @Mock
    private IndividualRoleRepository individualRoleRepository;

    @InjectMocks
    private DonationHeadService donationHeadService;

    private UUID testDonationHeadId;
    private UUID testUserId;
    private UUID testOrgId;
    private DonationHeadRequestDTO testRequestDTO;
    private DonationHeads testDonationHead;
    private DonationHeadResponseDTO testResponseDTO;

    @BeforeEach
    void setUp() {
        testDonationHeadId = UUID.randomUUID();
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();

        testRequestDTO = DonationHeadRequestDTO.builder()
                .orgId(testOrgId)
                .name("Test Donation Head")
                .description("Test Description")
                .build();

        testDonationHead = new DonationHeads();
        testDonationHead.setId(testDonationHeadId);
        testDonationHead.setOrgId(testOrgId);
        testDonationHead.setName("Test Donation Head");
        testDonationHead.setDescription("Test Description");
        testDonationHead.setIsActive(true);
        testDonationHead.setCreatedBy(testUserId);
        testDonationHead.setCreatedOn(LocalDateTime.now());

        testResponseDTO = DonationHeadResponseDTO.builder()
                .id(testDonationHeadId)
                .orgId(testOrgId)
                .name("Test Donation Head")
                .description("Test Description")
                .isActive(true)
                .createdBy(testUserId)
                .createdOn(LocalDateTime.now())
                .build();
    }

    @Test
    @DisplayName("Should create donation head successfully")
    void createDonationHead_Success() {
        // Given
        when(donationHeadRepository.existsByOrgIdAndName(testOrgId, "Test Donation Head"))
                .thenReturn(false);
        when(donationHeadMapper.donationHeadRequestDtoToDonationHead(eq(testRequestDTO), eq(testUserId), any(UUID.class)))
                .thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).saveDonationHead(testDonationHead);

        // When
        UUID result = donationHeadService.createDonationHead(testRequestDTO, testUserId);

        // Then
        assertNotNull(result);
        assertEquals(testDonationHeadId, result);
        verify(donationHeadRepository).existsByOrgIdAndName(testOrgId, "Test Donation Head");
        verify(donationHeadMapper).donationHeadRequestDtoToDonationHead(eq(testRequestDTO), eq(testUserId), any(UUID.class));
        verify(donationHeadRepository).saveDonationHead(testDonationHead);
    }

    @Test
    @DisplayName("Should throw exception when donation head already exists")
    void createDonationHead_AlreadyExists_ThrowsException() {
        // Given
        when(donationHeadRepository.existsByOrgIdAndName(testOrgId, "Test Donation Head"))
                .thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.createDonationHead(testRequestDTO, testUserId);
        });
        assertEquals("Donation Head Already exists", exception.getMessage());
        verify(donationHeadRepository).existsByOrgIdAndName(testOrgId, "Test Donation Head");
        verify(donationHeadRepository, never()).saveDonationHead(any());
    }

    @Test
    @DisplayName("Should update donation head successfully")
    void updateDonationHead_Success() {
        // Given
        DonationHeads updatedDonationHead = new DonationHeads();
        updatedDonationHead.setId(testDonationHeadId);
        updatedDonationHead.setUpdatedBy(testUserId);

        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        when(donationHeadMapper.updateDonationHeadFromDto(testRequestDTO, testDonationHead, testUserId))
                .thenReturn(updatedDonationHead);
        doNothing().when(donationHeadRepository).updateDonationHead(updatedDonationHead);

        // When
        donationHeadService.updateDonationHead(testDonationHeadId, testRequestDTO, testUserId);

        // Then
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadMapper).updateDonationHeadFromDto(testRequestDTO, testDonationHead, testUserId);
        verify(donationHeadRepository).updateDonationHead(updatedDonationHead);
    }

    @Test
    @DisplayName("Should throw exception when updating non-existent donation head")
    void updateDonationHead_NotFound_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.updateDonationHead(testDonationHeadId, testRequestDTO, testUserId);
        });
        assertEquals("DonationHead not found for ID: " + testDonationHeadId, exception.getMessage());
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadRepository, never()).updateDonationHead(any());
    }

    @Test
    @DisplayName("Should activate donation head successfully")
    void activateDonationHead_Success() {
        // Given
        testDonationHead.setIsActive(false);
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).updateDonationHead(testDonationHead);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donationHeadService.activateDonationHead(testDonationHeadId, testUserId);

            // Then
            assertTrue(testDonationHead.getIsActive());
            assertEquals(testUserId, testDonationHead.getUpdatedBy());
            assertEquals(mockTime, testDonationHead.getUpdatedOn());
            verify(donationHeadRepository).fetchOneById(testDonationHeadId);
            verify(donationHeadRepository).updateDonationHead(testDonationHead);
        }
    }

    @Test
    @DisplayName("Should throw exception when activating non-existent donation head")
    void activateDonationHead_NotFound_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.activateDonationHead(testDonationHeadId, testUserId);
        });
        assertEquals("DonationHead not found for ID: " + testDonationHeadId, exception.getMessage());
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadRepository, never()).updateDonationHead(any());
    }

    @Test
    @DisplayName("Should deactivate donation head successfully")
    void deactivateDonationHead_Success() {
        // Given
        testDonationHead.setIsActive(true);
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).updateDonationHead(testDonationHead);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donationHeadService.deactivateDonationHead(testDonationHeadId, testUserId);

            // Then
            assertFalse(testDonationHead.getIsActive());
            assertEquals(testUserId, testDonationHead.getUpdatedBy());
            assertEquals(mockTime, testDonationHead.getUpdatedOn());
            verify(donationHeadRepository).fetchOneById(testDonationHeadId);
            verify(donationHeadRepository).updateDonationHead(testDonationHead);
        }
    }

    @Test
    @DisplayName("Should get donation head by ID successfully")
    void getDonationHeadById_Success() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        when(donationHeadMapper.donationHeadToDonationHeadResponseDto(testDonationHead))
                .thenReturn(testResponseDTO);

        // When
        DonationHeadResponseDTO result = donationHeadService.getDonationHeadById(testDonationHeadId);

        // Then
        assertNotNull(result);
        assertEquals(testDonationHeadId, result.getId());
        assertEquals("Test Donation Head", result.getName());
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadMapper).donationHeadToDonationHeadResponseDto(testDonationHead);
    }

    @Test
    @DisplayName("Should throw exception when donation head not found by ID")
    void getDonationHeadById_NotFound_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.getDonationHeadById(testDonationHeadId);
        });
        assertEquals("DonationHead not found for ID: " + testDonationHeadId, exception.getMessage());
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadMapper, never()).donationHeadToDonationHeadResponseDto(any());
    }

    @Test
    @DisplayName("Should get donation heads by organization ID successfully")
    void getDonationHeadsByOrgId_Success() {
        // Given
        List<DonationHeads> mockDonationHeads = Arrays.asList(testDonationHead);
        when(donationHeadRepository.fetchByOrgId(testOrgId)).thenReturn(mockDonationHeads);

        // When
        DonationHeadsByOrgIdResponseDTO result = donationHeadService.getDonationHeadsByOrgId(testOrgId);

        // Then
        assertNotNull(result);
        assertNotNull(result.getDonationHeads());
        assertEquals(1, result.getDonationHeads().size());
        assertEquals(testDonationHeadId, result.getDonationHeads().get(0).getId());
        assertEquals("Test Donation Head", result.getDonationHeads().get(0).getName());
        verify(donationHeadRepository).fetchByOrgId(testOrgId);
    }

    @Test
    @DisplayName("Should apply default pagination values when null")
    void fetchAllDonationHeads_ApplyDefaults() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationHeadPaginationRequest request = DonationHeadPaginationRequest.builder().build();
        // Leave page and pageSize as null to test defaults

        List<Record> mockRecords = Arrays.asList();
        when(individualRoleRepository.applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationHeadRepository.fetchDonationHeadByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationHeadRepository.fetchDonationHeadCount(any())).thenReturn(0);

        // When
        GetAllDonationHeads result = donationHeadService.fetchAllDonationHeads(request, individualId);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), request.getPage()); // Default applied
        assertEquals(Integer.valueOf(10), request.getPageSize()); // Default applied
        verify(donationHeadRepository).fetchDonationHeadByPagination(eq(request), any(Condition.class));
    }

    @Test
    @DisplayName("Should get donation heads dropdown successfully")
    void getDonationHeads_Success() {
        // Given
        List<DonationHeads> mockDonationHeads = Arrays.asList(testDonationHead);
        when(donationHeadRepository.getAllDonationHeads()).thenReturn(mockDonationHeads);

        // When
        List<DonationHeadsDropDown> result = donationHeadService.getDonationHeads();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testDonationHeadId, result.get(0).getId());
        assertEquals("Test Donation Head", result.get(0).getDonationHeadName());
        verify(donationHeadRepository).getAllDonationHeads();
    }

    @Test
    @DisplayName("Should handle empty donation heads dropdown")
    void getDonationHeads_EmptyList_Success() {
        // Given
        when(donationHeadRepository.getAllDonationHeads()).thenReturn(Arrays.asList());

        // When
        List<DonationHeadsDropDown> result = donationHeadService.getDonationHeads();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(donationHeadRepository).getAllDonationHeads();
    }

    @Test
    @DisplayName("Should handle repository exception during creation")
    void createDonationHead_RepositoryException_ThrowsException() {
        // Given
        when(donationHeadRepository.existsByOrgIdAndName(testOrgId, "Test Donation Head"))
                .thenReturn(false);
        when(donationHeadMapper.donationHeadRequestDtoToDonationHead(any(), any(), any()))
                .thenReturn(testDonationHead);
        doThrow(new RuntimeException("Database error"))
                .when(donationHeadRepository).saveDonationHead(any());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.createDonationHead(testRequestDTO, testUserId);
        });
        assertEquals("Database error", exception.getMessage());
        verify(donationHeadRepository).saveDonationHead(testDonationHead);
    }
}
