package com.chidhagni.donationreceipt.donationhead;

import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadPaginationRequest;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadRequestDTO;
import com.chidhagni.donationreceipt.donationhead.dto.request.GetAllDonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsByOrgIdResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsDropDown;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for DonationHeadController
 * Tests the full HTTP request/response cycle with mocked service layer
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(DonationHeadController.class)
@DisplayName("DonationHeadController Integration Tests")
class DonationHeadControllerITest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DonationHeadService donationHeadService;

    @Autowired
    private ObjectMapper objectMapper;

    private UUID testDonationHeadId;
    private UUID testUserId;
    private UUID testOrgId;
    private DonationHeadRequestDTO testRequestDTO;
    private DonationHeadResponseDTO testResponseDTO;
    private UserPrincipal testUserPrincipal;

    @BeforeEach
    void setUp() {
        testDonationHeadId = UUID.randomUUID();
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();

        testRequestDTO = DonationHeadRequestDTO.builder()
                .orgId(testOrgId)
                .name("Test Donation Head")
                .description("Test Description")
                .build();

        testResponseDTO = DonationHeadResponseDTO.builder()
                .id(testDonationHeadId)
                .orgId(testOrgId)
                .name("Test Donation Head")
                .description("Test Description")
                .isActive(true)
                .createdOn(LocalDateTime.now())
                .createdBy(testUserId)
                .build();

        testUserPrincipal = UserPrincipal.builder()
                .id(testUserId)
                .email("<EMAIL>")
                .build();
    }

    @Test
    @DisplayName("Should create donation head successfully")
    @WithMockUser
    void createDonationHead_Success() throws Exception {
        // Given
        when(donationHeadService.createDonationHead(any(DonationHeadRequestDTO.class), eq(testUserId)))
                .thenReturn(testDonationHeadId);

        // When & Then
        mockMvc.perform(post("/donation-heads")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testDonationHeadId.toString()));

        verify(donationHeadService).createDonationHead(any(DonationHeadRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should return bad request for invalid donation head data")
    @WithMockUser
    void createDonationHead_InvalidData_BadRequest() throws Exception {
        // Given - Invalid request with null required fields
        DonationHeadRequestDTO invalidRequest = DonationHeadRequestDTO.builder().build();

        // When & Then
        mockMvc.perform(post("/donation-heads")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        verify(donationHeadService, never()).createDonationHead(any(), any());
    }

    @Test
    @DisplayName("Should handle service exception during creation")
    @WithMockUser
    void createDonationHead_ServiceException_InternalServerError() throws Exception {
        // Given
        when(donationHeadService.createDonationHead(any(DonationHeadRequestDTO.class), eq(testUserId)))
                .thenThrow(new RuntimeException("Donation Head Already exists"));

        // When & Then
        mockMvc.perform(post("/donation-heads")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isInternalServerError());

        verify(donationHeadService).createDonationHead(any(DonationHeadRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should update donation head successfully")
    @WithMockUser
    void updateDonationHead_Success() throws Exception {
        // Given
        doNothing().when(donationHeadService)
                .updateDonationHead(eq(testDonationHeadId), any(DonationHeadRequestDTO.class), eq(testUserId));

        // When & Then
        mockMvc.perform(put("/donation-heads/{id}", testDonationHeadId)
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isOk());

        verify(donationHeadService).updateDonationHead(eq(testDonationHeadId), any(DonationHeadRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should activate donation head successfully")
    @WithMockUser
    void activateDonationHead_Success() throws Exception {
        // Given
        doNothing().when(donationHeadService).activateDonationHead(testDonationHeadId, testUserId);

        // When & Then
        mockMvc.perform(patch("/donation-heads/{id}", testDonationHeadId)
                        .with(user(testUserPrincipal)))
                .andExpect(status().isOk());

        verify(donationHeadService).activateDonationHead(testDonationHeadId, testUserId);
    }

    @Test
    @DisplayName("Should deactivate donation head successfully")
    @WithMockUser
    void deactivateDonationHead_Success() throws Exception {
        // Given
        doNothing().when(donationHeadService).deactivateDonationHead(testDonationHeadId, testUserId);

        // When & Then
        mockMvc.perform(delete("/donation-heads/{id}", testDonationHeadId)
                        .with(user(testUserPrincipal)))
                .andExpect(status().isOk());

        verify(donationHeadService).deactivateDonationHead(testDonationHeadId, testUserId);
    }

    @Test
    @DisplayName("Should fetch all donation heads successfully")
    @WithMockUser
    void fetchAllDonationHeads_Success() throws Exception {
        // Given
        DonationHeadPaginationRequest paginationRequest = DonationHeadPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .build();

        GetAllDonationHeads mockResponse = GetAllDonationHeads.builder()
                .donationHeads(Arrays.asList(testResponseDTO))
                .rowCount(1)
                .build();

        when(donationHeadService.fetchAllDonationHeads(any(DonationHeadPaginationRequest.class), eq(testUserId)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/donation-heads/all")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(paginationRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rowCount").value(1))
                .andExpect(jsonPath("$.donationHeads").isArray())
                .andExpect(jsonPath("$.donationHeads[0].id").value(testDonationHeadId.toString()));

        verify(donationHeadService).fetchAllDonationHeads(any(DonationHeadPaginationRequest.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should fetch all donation heads with null pagination request")
    @WithMockUser
    void fetchAllDonationHeads_NullPaginationRequest_Success() throws Exception {
        // Given
        GetAllDonationHeads mockResponse = GetAllDonationHeads.builder()
                .donationHeads(Arrays.asList(testResponseDTO))
                .rowCount(1)
                .build();

        when(donationHeadService.fetchAllDonationHeads(any(DonationHeadPaginationRequest.class), eq(testUserId)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/donation-heads/all")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rowCount").value(1));

        verify(donationHeadService).fetchAllDonationHeads(any(DonationHeadPaginationRequest.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should get donation head by ID successfully")
    @WithMockUser
    void getDonationHeadById_Success() throws Exception {
        // Given
        when(donationHeadService.getDonationHeadById(testDonationHeadId)).thenReturn(testResponseDTO);

        // When & Then
        mockMvc.perform(get("/donation-heads/{id}", testDonationHeadId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testDonationHeadId.toString()))
                .andExpect(jsonPath("$.name").value("Test Donation Head"))
                .andExpect(jsonPath("$.description").value("Test Description"))
                .andExpect(jsonPath("$.orgId").value(testOrgId.toString()))
                .andExpect(jsonPath("$.isActive").value(true));

        verify(donationHeadService).getDonationHeadById(testDonationHeadId);
    }

    @Test
    @DisplayName("Should get donation heads by organization ID successfully")
    @WithMockUser
    void getDonationHeadsByOrgId_Success() throws Exception {
        // Given
        DonationHeadsByOrgIdResponseDTO mockResponse = new DonationHeadsByOrgIdResponseDTO(
                Arrays.asList(new DonationHeadsByOrgIdResponseDTO.DonationHeadSummary(testDonationHeadId, "Test Donation Head"))
        );

        when(donationHeadService.getDonationHeadsByOrgId(testOrgId)).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(get("/donation-heads/by-org/{orgId}", testOrgId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.donationHeads").isArray())
                .andExpect(jsonPath("$.donationHeads[0].id").value(testDonationHeadId.toString()))
                .andExpect(jsonPath("$.donationHeads[0].name").value("Test Donation Head"));

        verify(donationHeadService).getDonationHeadsByOrgId(testOrgId);
    }

    @Test
    @DisplayName("Should get all donation heads dropdown successfully")
    @WithMockUser
    void getAllDonationHeads_Success() throws Exception {
        // Given
        List<DonationHeadsDropDown> mockDropdowns = Arrays.asList(
                DonationHeadsDropDown.builder()
                        .id(testDonationHeadId)
                        .donationHeadName("Test Donation Head")
                        .build()
        );

        when(donationHeadService.getDonationHeads()).thenReturn(mockDropdowns);

        // When & Then
        mockMvc.perform(get("/donation-heads/get-donation-heads"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].id").value(testDonationHeadId.toString()))
                .andExpect(jsonPath("$[0].donationHeadName").value("Test Donation Head"));

        verify(donationHeadService).getDonationHeads();
    }

    @Test
    @DisplayName("Should handle unauthorized access")
    void createDonationHead_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/donation-heads")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isUnauthorized());

        verify(donationHeadService, never()).createDonationHead(any(), any());
    }

    @Test
    @DisplayName("Should handle invalid UUID in path parameter")
    @WithMockUser
    void getDonationHeadById_InvalidUUID_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/donation-heads/{id}", "invalid-uuid"))
                .andExpect(status().isBadRequest());

        verify(donationHeadService, never()).getDonationHeadById(any());
    }

    @Test
    @DisplayName("Should handle service exception during get by ID")
    @WithMockUser
    void getDonationHeadById_ServiceException_InternalServerError() throws Exception {
        // Given
        when(donationHeadService.getDonationHeadById(testDonationHeadId))
                .thenThrow(new RuntimeException("DonationHead not found for ID: " + testDonationHeadId));

        // When & Then
        mockMvc.perform(get("/donation-heads/{id}", testDonationHeadId))
                .andExpect(status().isInternalServerError());

        verify(donationHeadService).getDonationHeadById(testDonationHeadId);
    }
}
