package com.chidhagni.donationreceipt.donationhead;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadPaginationRequest;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadRequestDTO;
import com.chidhagni.donationreceipt.donationhead.dto.request.GetAllDonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsByOrgIdResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsDropDown;
import com.chidhagni.donationreceipt.donationhead.utils.DonationHeadMapper;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.utils.DateUtils;
import org.jooq.Condition;
import org.jooq.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DonationHeadService Unit Tests")
class DonationHeadServiceTest {

    @Mock
    private DonationHeadRepository donationHeadRepository;

    @Mock
    private DonationHeadMapper donationHeadMapper;

    @Mock
    private IndividualRoleRepository individualRoleRepository;

    @InjectMocks
    private DonationHeadService donationHeadService;

    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonationHeadId;
    private DonationHeadRequestDTO testRequestDTO;
    private DonationHeads testDonationHead;
    private DonationHeadResponseDTO testResponseDTO;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonationHeadId = UUID.randomUUID();

        testRequestDTO = DonationHeadRequestDTO.builder()
                .orgId(testOrgId)
                .name("Test Donation Head")
                .description("Test Description")
                .build();

        testDonationHead = new DonationHeads();
        testDonationHead.setId(testDonationHeadId);
        testDonationHead.setName("Test Donation Head");
        testDonationHead.setDescription("Test Description");
        testDonationHead.setOrgId(testOrgId);
        testDonationHead.setIsActive(true);
        testDonationHead.setCreatedBy(testUserId);
        testDonationHead.setCreatedOn(LocalDateTime.now());
        testDonationHead.setUpdatedBy(testUserId);
        testDonationHead.setUpdatedOn(LocalDateTime.now());

        testResponseDTO = DonationHeadResponseDTO.builder()
                .id(testDonationHeadId)
                .name("Test Donation Head")
                .description("Test Description")
                .orgId(testOrgId)
                .isActive(true)
                .createdBy(testUserId)
                .createdOn(LocalDateTime.now())
                .updatedBy(testUserId)
                .updatedOn(LocalDateTime.now())
                .build();
    }

    @Test
    @DisplayName("Should create donation head successfully")
    void createDonationHead_Success() {
        // Given
        when(donationHeadRepository.existsByOrgIdAndName(testOrgId, "Test Donation Head"))
                .thenReturn(false);
        when(donationHeadMapper.donationHeadRequestDtoToDonationHead(any(), any(), any()))
                .thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).saveDonationHead(any());

        // When
        UUID result = donationHeadService.createDonationHead(testRequestDTO, testUserId);

        // Then
        assertNotNull(result);
        assertEquals(testDonationHeadId, result);
        verify(donationHeadRepository).existsByOrgIdAndName(testOrgId, "Test Donation Head");
        verify(donationHeadMapper).donationHeadRequestDtoToDonationHead(any(), any(), any());
        verify(donationHeadRepository).saveDonationHead(testDonationHead);
    }

    @Test
    @DisplayName("Should throw exception when donation head already exists")
    void createDonationHead_AlreadyExists_ThrowsException() {
        // Given
        when(donationHeadRepository.existsByOrgIdAndName(testOrgId, "Test Donation Head"))
                .thenReturn(true);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            donationHeadService.createDonationHead(testRequestDTO, testUserId);
        });
        verify(donationHeadRepository).existsByOrgIdAndName(testOrgId, "Test Donation Head");
        verify(donationHeadRepository, never()).saveDonationHead(any());
    }

    @Test
    @DisplayName("Should update donation head successfully")
    void updateDonationHead_Success() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        when(donationHeadMapper.updateDonationHeadFromDto(any(), any(), any()))
                .thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).updateDonationHead(any());

        // When
        donationHeadService.updateDonationHead(testDonationHeadId, testRequestDTO, testUserId);

        // Then
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadMapper).updateDonationHeadFromDto(testRequestDTO, testDonationHead, testUserId);
        verify(donationHeadRepository).updateDonationHead(testDonationHead);
    }

    @Test
    @DisplayName("Should throw exception when updating non-existent donation head")
    void updateDonationHead_NotFound_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(null);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            donationHeadService.updateDonationHead(testDonationHeadId, testRequestDTO, testUserId);
        });
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadRepository, never()).updateDonationHead(any());
    }

    @Test
    @DisplayName("Should activate donation head successfully")
    void activateDonationHead_Success() {
        // Given
        testDonationHead.setIsActive(false);
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).updateDonationHead(any());

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donationHeadService.activateDonationHead(testDonationHeadId, testUserId);

            // Then
            assertTrue(testDonationHead.getIsActive());
            assertEquals(testUserId, testDonationHead.getUpdatedBy());
            assertEquals(mockTime, testDonationHead.getUpdatedOn());
            verify(donationHeadRepository).fetchOneById(testDonationHeadId);
            verify(donationHeadRepository).updateDonationHead(testDonationHead);
        }
    }

    @Test
    @DisplayName("Should deactivate donation head successfully")
    void deactivateDonationHead_Success() {
        // Given
        testDonationHead.setIsActive(true);
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        doNothing().when(donationHeadRepository).updateDonationHead(any());

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donationHeadService.deactivateDonationHead(testDonationHeadId, testUserId);

            // Then
            assertFalse(testDonationHead.getIsActive());
            assertEquals(testUserId, testDonationHead.getUpdatedBy());
            assertEquals(mockTime, testDonationHead.getUpdatedOn());
            verify(donationHeadRepository).fetchOneById(testDonationHeadId);
            verify(donationHeadRepository).updateDonationHead(testDonationHead);
        }
    }

    @Test
    @DisplayName("Should fetch all donation heads with pagination")
    void fetchAllDonationHeads_Success() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationHeadPaginationRequest request = DonationHeadPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .searchKeyWord("test")
                .build();

        List<Record> mockRecords = Arrays.asList(mock(Record.class), mock(Record.class));
        when(individualRoleRepository.applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationHeadRepository.fetchDonationHeadByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationHeadRepository.fetchDonationHeadCount(any())).thenReturn(2);

        // Mock the mapping method - we'll use reflection or remove this test since it's a private method

        // When
        GetAllDonationHeads result = donationHeadService.fetchAllDonationHeads(request, individualId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getRowCount());
        assertEquals(2, result.getDonationHeads().size());
        verify(individualRoleRepository).applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class));
        verify(donationHeadRepository).fetchDonationHeadByPagination(eq(request), any(Condition.class));
        verify(donationHeadRepository).fetchDonationHeadCount(any());
    }

    @Test
    @DisplayName("Should get donation head by ID successfully")
    void getDonationHeadById_Success() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        when(donationHeadMapper.donationHeadToResponseDTO(testDonationHead))
                .thenReturn(testResponseDTO);

        // When
        DonationHeadResponseDTO result = donationHeadService.getDonationHeadById(testDonationHeadId);

        // Then
        assertNotNull(result);
        assertEquals(testDonationHeadId, result.getId());
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadMapper).donationHeadToResponseDTO(testDonationHead);
    }

    @Test
    @DisplayName("Should throw exception when getting non-existent donation head by ID")
    void getDonationHeadById_NotFound_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(null);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            donationHeadService.getDonationHeadById(testDonationHeadId);
        });
        verify(donationHeadRepository).fetchOneById(testDonationHeadId);
        verify(donationHeadMapper, never()).donationHeadToResponseDTO(any());
    }

    @Test
    @DisplayName("Should get donation heads by org ID successfully")
    void getDonationHeadsByOrgId_Success() {
        // Given
        List<Record> mockRecords = Arrays.asList(mock(Record.class));
        when(donationHeadRepository.findAllByOrgId(testOrgId)).thenReturn(mockRecords);

        // When
        DonationHeadsByOrgIdResponseDTO result = donationHeadService.getDonationHeadsByOrgId(testOrgId);

        // Then
        assertNotNull(result);
        verify(donationHeadRepository).findAllByOrgId(testOrgId);
    }

    @Test
    @DisplayName("Should get donation heads dropdown successfully")
    void getDonationHeads_Success() {
        // Given
        List<DonationHeadsDropDown> mockDropdowns = Arrays.asList(
            DonationHeadsDropDown.builder()
                .id(testDonationHeadId)
                .donationHeadName("Test Donation Head")
                .build()
        );
        when(donationHeadRepository.getAllDonationHeads()).thenReturn(mockDropdowns);

        // When
        List<DonationHeadsDropDown> result = donationHeadService.getDonationHeads();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testDonationHeadId, result.get(0).getId());
        verify(donationHeadRepository).getAllDonationHeads();
    }

    @Test
    @DisplayName("Should apply default pagination values when null")
    void fetchAllDonationHeads_ApplyDefaults() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationHeadPaginationRequest request = DonationHeadPaginationRequest.builder().build();
        // Leave page and pageSize as null to test defaults

        List<Record> mockRecords = Arrays.asList();
        when(individualRoleRepository.applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationHeadRepository.fetchDonationHeadByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationHeadRepository.fetchDonationHeadCount(any())).thenReturn(0);

        // When
        GetAllDonationHeads result = donationHeadService.fetchAllDonationHeads(request, individualId);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), request.getPage()); // Default applied
        assertEquals(Integer.valueOf(10), request.getPageSize()); // Default applied
        verify(donationHeadRepository).fetchDonationHeadByPagination(eq(request), any(Condition.class));
    }

    @Test
    @DisplayName("Should build search condition with all filters")
    void fetchAllDonationHeads_WithAllFilters_Success() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationHeadPaginationRequest request = DonationHeadPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .nameFilter("Test Name")
                .descriptionFilter("Test Description")
                .orgIdFilter(testOrgId)
                .searchKeyWord("keyword")
                .build();

        List<Record> mockRecords = Arrays.asList();
        when(individualRoleRepository.applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationHeadRepository.fetchDonationHeadByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationHeadRepository.fetchDonationHeadCount(any())).thenReturn(0);

        // When
        GetAllDonationHeads result = donationHeadService.fetchAllDonationHeads(request, individualId);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getRowCount());
        verify(individualRoleRepository).applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class));
        verify(donationHeadRepository).fetchDonationHeadByPagination(eq(request), any(Condition.class));
        verify(donationHeadRepository).fetchDonationHeadCount(any());
    }

    @Test
    @DisplayName("Should handle null search keyword")
    void fetchAllDonationHeads_NullSearchKeyword_Success() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationHeadPaginationRequest request = DonationHeadPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .searchKeyWord(null) // Explicitly set to null
                .build();

        List<Record> mockRecords = Arrays.asList();
        when(individualRoleRepository.applyOrganizationFilterDonationHead(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationHeadRepository.fetchDonationHeadByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationHeadRepository.fetchDonationHeadCount(any())).thenReturn(0);

        // When
        GetAllDonationHeads result = donationHeadService.fetchAllDonationHeads(request, individualId);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getRowCount());
        verify(donationHeadRepository).fetchDonationHeadByPagination(eq(request), any(Condition.class));
    }

    @Test
    @DisplayName("Should handle empty donation heads dropdown")
    void getDonationHeads_EmptyList_Success() {
        // Given
        when(donationHeadRepository.getAllDonationHeads()).thenReturn(Arrays.asList());

        // When
        List<DonationHeadsDropDown> result = donationHeadService.getDonationHeads();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(donationHeadRepository).getAllDonationHeads();
    }

    @Test
    @DisplayName("Should handle repository exception during creation")
    void createDonationHead_RepositoryException_ThrowsException() {
        // Given
        when(donationHeadRepository.existsByOrgIdAndName(testOrgId, "Test Donation Head"))
                .thenReturn(false);
        when(donationHeadMapper.donationHeadRequestDtoToDonationHead(any(), any(), any()))
                .thenReturn(testDonationHead);
        doThrow(new RuntimeException("Database error"))
                .when(donationHeadRepository).saveDonationHead(any());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.createDonationHead(testRequestDTO, testUserId);
        });
        assertEquals("Database error", exception.getMessage());
        verify(donationHeadRepository).saveDonationHead(testDonationHead);
    }

    @Test
    @DisplayName("Should handle repository exception during update")
    void updateDonationHead_RepositoryException_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        when(donationHeadMapper.updateDonationHeadFromDto(testRequestDTO, testDonationHead, testUserId))
                .thenReturn(testDonationHead);
        doThrow(new RuntimeException("Update failed"))
                .when(donationHeadRepository).updateDonationHead(any());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.updateDonationHead(testDonationHeadId, testRequestDTO, testUserId);
        });
        assertEquals("Update failed", exception.getMessage());
        verify(donationHeadRepository).updateDonationHead(testDonationHead);
    }

    @Test
    @DisplayName("Should handle repository exception during activation")
    void activateDonationHead_RepositoryException_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        doThrow(new RuntimeException("Activation failed"))
                .when(donationHeadRepository).updateDonationHead(any());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.activateDonationHead(testDonationHeadId, testUserId);
        });
        assertEquals("Activation failed", exception.getMessage());
        verify(donationHeadRepository).updateDonationHead(testDonationHead);
    }

    @Test
    @DisplayName("Should handle repository exception during deactivation")
    void deactivateDonationHead_RepositoryException_ThrowsException() {
        // Given
        when(donationHeadRepository.fetchOneById(testDonationHeadId)).thenReturn(testDonationHead);
        doThrow(new RuntimeException("Deactivation failed"))
                .when(donationHeadRepository).updateDonationHead(any());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            donationHeadService.deactivateDonationHead(testDonationHeadId, testUserId);
        });
        assertEquals("Deactivation failed", exception.getMessage());
        verify(donationHeadRepository).updateDonationHead(testDonationHead);
    }
}