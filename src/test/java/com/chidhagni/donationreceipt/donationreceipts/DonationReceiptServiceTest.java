package com.chidhagni.donationreceipt.donationreceipts;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts;
import com.chidhagni.donationreceipt.donationhead.DonationHeadRepository;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptPaginationRequest;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptRequestDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.GetAllDonationReceipts;
import com.chidhagni.donationreceipt.donationreceipts.utils.DonationReceiptMapper;
import com.chidhagni.donationreceipt.donationreceipts.utils.DonationReceiptsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.services.listvalues.ListValuesRepository;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.donationreceipt.member.SystemCodes;
import com.chidhagni.utils.CommonOperations;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.PdfGenerateService;
import org.jooq.Condition;
import org.jooq.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DonationReceiptService Unit Tests")
class DonationReceiptServiceTest {

    @Mock
    private DonationReceiptRepository donationReceiptRepository;

    @Mock
    private DonationReceiptMapper donationReceiptMapper;

    @Mock
    private IndividualRoleRepository individualRoleRepository;

    @Mock
    private CommonOperations commonOperations;

    @Mock
    private DonationHeadRepository donationHeadRepository;

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private IndividualRepository individualRepository;

    @Mock
    private ListValuesRepository listValuesRepository;

    @Mock
    private PdfGenerateService pdfGenerateService;

    @Mock
    private SpringTemplateEngine templateEngine;

    @Mock
    private NotificationManager notificationManager;

    @Mock
    private DonorsRepository donorsRepository;

    @Mock
    private DonationReceiptsMapper donationReceiptsMapper;

    @Mock
    private WatiService watiService;

    @InjectMocks
    private DonationReceiptService donationReceiptService;

    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonorId;
    private UUID testDonationHeadId;
    private UUID testReceiptId;
    private DonationReceiptRequestDTO testRequestDTO;
    private DonationReceipts testReceipt;
    private DonationReceiptResponseDTO testResponseDTO;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonorId = UUID.randomUUID();
        testDonationHeadId = UUID.randomUUID();
        testReceiptId = UUID.randomUUID();

        testRequestDTO = DonationReceiptRequestDTO.builder()
                .donorId(testDonorId)
                .donationHeadId(testDonationHeadId)
                .receiptDate("2024-01-01")
                .donationTypeId(UUID.randomUUID())
                .orgId(testOrgId)
                .build();

        testReceipt = new DonationReceipts();
        testReceipt.setId(testReceiptId);
        testReceipt.setDonorId(testDonorId);
        testReceipt.setDonationHeadId(testDonationHeadId);
        testReceipt.setReceiptNo("DR001");
        testReceipt.setIsActive(true);
        testReceipt.setCreatedBy(testUserId);
        testReceipt.setCreatedOn(LocalDateTime.now());

        testResponseDTO = DonationReceiptResponseDTO.builder()
                .id(testReceiptId)
                .receiptNo("DR001")
                .isActive(true)
                .build();

        // Set private fields using reflection
        ReflectionTestUtils.setField(donationReceiptService, "token", "test-token");
        ReflectionTestUtils.setField(donationReceiptService, "donationReceiptPdf", "test-pdf");
    }


    @Test
    @DisplayName("Should activate donation receipt successfully")
    void activateDonationReceipt_Success() {
        // Given
        testReceipt.setIsActive(false);
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentDatetime).thenReturn(mockTime);

            // When
            donationReceiptService.activateDonationReceipt(testReceiptId, testUserId);

            // Then
            assertTrue(testReceipt.getIsActive());
            assertEquals(testUserId, testReceipt.getUpdatedBy());
            assertEquals(mockTime, testReceipt.getUpdatedOn());
            verify(donationReceiptRepository).updateDonationReceipt(testReceipt);
        }
    }

    @Test
    @DisplayName("Should deactivate donation receipt successfully")
    void deactivateDonationReceipt_Success() {
        // Given
        testReceipt.setIsActive(true);
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentDatetime).thenReturn(mockTime);

            // When
            donationReceiptService.deactivateDonationReceipt(testReceiptId, testUserId);

            // Then
            assertFalse(testReceipt.getIsActive());
            assertEquals(testUserId, testReceipt.getUpdatedBy());
            assertEquals(mockTime, testReceipt.getUpdatedOn());
            verify(donationReceiptRepository).updateDonationReceipt(testReceipt);
        }
    }

    @Test
    @DisplayName("Should fetch all donation receipts successfully")
    void fetchAllDonationReceipts_Success() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationReceiptPaginationRequest request = new DonationReceiptPaginationRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setSearchKeyWord("test");

        List<Record> mockRecords = Arrays.asList(mock(Record.class), mock(Record.class));
        when(individualRoleRepository.applyOrganizationFilterDonationReceipt(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationReceiptRepository.fetchDonationReceiptByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationReceiptRepository.fetchDonationReceiptCount(any())).thenReturn(2);

        // When
        GetAllDonationReceipts result = donationReceiptService.fetchAllDonationReceipts(request, individualId);

        // Then
        assertNotNull(result);
        verify(individualRoleRepository).applyOrganizationFilterDonationReceipt(eq(individualId), any(Condition.class));
        verify(donationReceiptRepository).fetchDonationReceiptByPagination(eq(request), any(Condition.class));
        verify(donationReceiptRepository).fetchDonationReceiptCount(any(Condition.class));
    }

    @Test
    @DisplayName("Should get donation receipt or throw exception when not found")
    void getDonationReceiptOrThrow_Success() {
        // Given
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);

        // When
        DonationReceipts result = donationReceiptService.getDonationReceiptOrThrow(testReceiptId);

        // Then
        assertNotNull(result);
        assertEquals(testReceiptId, result.getId());
        verify(donationReceiptRepository).fetchOneById(testReceiptId);
    }

    @Test
    @DisplayName("Should throw exception when donation receipt not found")
    void getDonationReceiptOrThrow_NotFound_ThrowsException() {
        // Given
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(null);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            donationReceiptService.getDonationReceiptOrThrow(testReceiptId);
        });
        verify(donationReceiptRepository).fetchOneById(testReceiptId);
    }
} 