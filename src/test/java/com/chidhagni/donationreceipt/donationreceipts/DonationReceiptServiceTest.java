package com.chidhagni.donationreceipt.donationreceipts;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.*;
import com.chidhagni.donationreceipt.donationhead.DonationHeadRepository;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptPaginationRequest;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptRequestDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.GetAllDonationReceipts;
import com.chidhagni.donationreceipt.donationreceipts.utils.DonationReceiptMapper;
import com.chidhagni.donationreceipt.donationreceipts.utils.DonationReceiptsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.services.listvalues.ListValuesRepository;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.donationreceipt.member.SystemCodes;
import com.chidhagni.utils.CommonOperations;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.PdfGenerateService;
import org.jooq.Condition;
import org.jooq.Record;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DonationReceiptService Unit Tests")
class DonationReceiptServiceTest {

    @Mock
    private DonationReceiptRepository donationReceiptRepository;

    @Mock
    private DonationReceiptMapper donationReceiptMapper;

    @Mock
    private IndividualRoleRepository individualRoleRepository;

    @Mock
    private CommonOperations commonOperations;

    @Mock
    private DonationHeadRepository donationHeadRepository;

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private IndividualRepository individualRepository;

    @Mock
    private ListValuesRepository listValuesRepository;

    @Mock
    private PdfGenerateService pdfGenerateService;

    @Mock
    private SpringTemplateEngine templateEngine;

    @Mock
    private NotificationManager notificationManager;

    @Mock
    private DonorsRepository donorsRepository;

    @Mock
    private DonationReceiptsMapper donationReceiptsMapper;

    @Mock
    private WatiService watiService;

    @InjectMocks
    private DonationReceiptService donationReceiptService;

    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonorId;
    private UUID testDonationHeadId;
    private UUID testReceiptId;
    private DonationReceiptRequestDTO testRequestDTO;
    private DonationReceipts testReceipt;

    private static final String RECEIPT_TEMPLATE = "receipt-generation-pdf.html";

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonorId = UUID.randomUUID();
        testDonationHeadId = UUID.randomUUID();
        testReceiptId = UUID.randomUUID();

        testRequestDTO = DonationReceiptRequestDTO.builder()
                .donorId(testDonorId)
                .donationHeadId(testDonationHeadId)
                .receiptDate("2024-01-01")
                .donationTypeId(UUID.randomUUID())
                .orgId(testOrgId)
                .build();

        testReceipt = new DonationReceipts();
        testReceipt.setId(testReceiptId);
        testReceipt.setDonorId(testDonorId);
        testReceipt.setDonationHeadId(testDonationHeadId);
        testReceipt.setReceiptNo("DR001");
        testReceipt.setIsActive(true);
        testReceipt.setCreatedBy(testUserId);
        testReceipt.setCreatedOn(LocalDateTime.now());

        // Set private fields using reflection
        ReflectionTestUtils.setField(donationReceiptService, "token", "test-token");
        ReflectionTestUtils.setField(donationReceiptService, "donationReceiptPdf", "test-pdf");
    }


    @Test
    @DisplayName("Should activate donation receipt successfully")
    void activateDonationReceipt_Success() {
        // Given
        testReceipt.setIsActive(false);
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentDatetime).thenReturn(mockTime);

            // When
            donationReceiptService.activateDonationReceipt(testReceiptId, testUserId);

            // Then
            assertTrue(testReceipt.getIsActive());
            assertEquals(testUserId, testReceipt.getUpdatedBy());
            assertEquals(mockTime, testReceipt.getUpdatedOn());
            verify(donationReceiptRepository).updateDonationReceipt(testReceipt);
        }
    }

    @Test
    @DisplayName("Should deactivate donation receipt successfully")
    void deactivateDonationReceipt_Success() {
        // Given
        testReceipt.setIsActive(true);
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentDatetime).thenReturn(mockTime);

            // When
            donationReceiptService.deactivateDonationReceipt(testReceiptId, testUserId);

            // Then
            assertFalse(testReceipt.getIsActive());
            assertEquals(testUserId, testReceipt.getUpdatedBy());
            assertEquals(mockTime, testReceipt.getUpdatedOn());
            verify(donationReceiptRepository).updateDonationReceipt(testReceipt);
        }
    }

    @Test
    @DisplayName("Should fetch all donation receipts successfully")
    void fetchAllDonationReceipts_Success() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationReceiptPaginationRequest request = new DonationReceiptPaginationRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setSearchKeyWord("test");

        List<Record> mockRecords = Arrays.asList(mock(Record.class), mock(Record.class));
        when(individualRoleRepository.applyOrganizationFilterDonationReceipt(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationReceiptRepository.fetchDonationReceiptByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationReceiptRepository.fetchDonationReceiptCount(any())).thenReturn(2);

        // When
        GetAllDonationReceipts result = donationReceiptService.fetchAllDonationReceipts(request, individualId);

        // Then
        assertNotNull(result);
        verify(individualRoleRepository).applyOrganizationFilterDonationReceipt(eq(individualId), any(Condition.class));
        verify(donationReceiptRepository).fetchDonationReceiptByPagination(eq(request), any(Condition.class));
        verify(donationReceiptRepository).fetchDonationReceiptCount(any(Condition.class));
    }

    @Test
    @DisplayName("Should get donation receipt or throw exception when not found")
    void getDonationReceiptOrThrow_Success() {
        // Given
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);

        // When
        DonationReceipts result = donationReceiptService.getDonationReceiptOrThrow(testReceiptId);

        // Then
        assertNotNull(result);
        assertEquals(testReceiptId, result.getId());
        verify(donationReceiptRepository).fetchOneById(testReceiptId);
    }

    @Test
    @DisplayName("Should throw exception when donation receipt not found")
    void getDonationReceiptOrThrow_NotFound_ThrowsException() {
        // Given
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(null);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            donationReceiptService.getDonationReceiptOrThrow(testReceiptId);
        });
        verify(donationReceiptRepository).fetchOneById(testReceiptId);
    }

    @Test
    @DisplayName("Should create donation receipt successfully")
    void createDonationReceipt_Success() {
        // Given
        String generatedReceiptNo = "DR001";

        try (MockedStatic<CommonOperations> commonOpsMock = mockStatic(CommonOperations.class)) {
            commonOpsMock.when(() -> CommonOperations.generateSystemCode(SystemCodes.DR)).thenReturn(generatedReceiptNo);
            when(donationReceiptMapper.donationReceiptRequestDtoToDonationReceipt(eq(testRequestDTO), eq(testUserId), any(UUID.class)))
                    .thenReturn(testReceipt);

            // When
            UUID result = donationReceiptService.createDonationReceipt(testRequestDTO, testUserId);

            // Then
            assertNotNull(result);
            assertEquals(testReceiptId, result);
            assertEquals(generatedReceiptNo, testReceipt.getReceiptNo());
            commonOpsMock.verify(() -> CommonOperations.generateSystemCode(SystemCodes.DR));
            verify(donationReceiptMapper).donationReceiptRequestDtoToDonationReceipt(eq(testRequestDTO), eq(testUserId), any(UUID.class));
            verify(donationReceiptRepository).saveDonationReceipt(testReceipt);
        }
    }

    @Test
    @DisplayName("Should update donation receipt successfully")
    void updateDonationReceipt_Success() {
        // Given
        DonationReceipts updatedReceipt = new DonationReceipts();
        updatedReceipt.setId(testReceiptId);
        updatedReceipt.setUpdatedBy(testUserId);

        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(testReceipt);
        when(donationReceiptMapper.updateDonationReceiptFromDto(testRequestDTO, testReceipt, testUserId))
                .thenReturn(updatedReceipt);

        // When
        donationReceiptService.updateDonationReceipt(testReceiptId, testRequestDTO, testUserId);

        // Then
        verify(donationReceiptRepository).fetchOneById(testReceiptId);
        verify(donationReceiptMapper).updateDonationReceiptFromDto(testRequestDTO, testReceipt, testUserId);
        verify(donationReceiptRepository).updateDonationReceipt(updatedReceipt);
    }

    @Test
    @DisplayName("Should throw exception when updating non-existent donation receipt")
    void updateDonationReceipt_NotFound_ThrowsException() {
        // Given
        when(donationReceiptRepository.fetchOneById(testReceiptId)).thenReturn(null);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            donationReceiptService.updateDonationReceipt(testReceiptId, testRequestDTO, testUserId);
        });
        verify(donationReceiptRepository).fetchOneById(testReceiptId);
        verify(donationReceiptMapper, never()).updateDonationReceiptFromDto(any(), any(), any());
        verify(donationReceiptRepository, never()).updateDonationReceipt(any());
    }

    @Test
    @DisplayName("Should get donation receipt by ID successfully")
    void getDonationReceiptById_Success() {
        // Given
        Record mockRecord = mock(Record.class);
        when(donationReceiptRepository.fetchOneByIdIncludesOrgNameDonationHead(testReceiptId)).thenReturn(mockRecord);

        // Mock record field access using specific field types
        when(mockRecord.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.ID))
                .thenReturn(testReceiptId);
        when(mockRecord.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.RECEIPT_NO))
                .thenReturn("DR001");
        when(mockRecord.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.IS_ACTIVE))
                .thenReturn(true);
        when(mockRecord.get("orgName", String.class)).thenReturn("Test Org");
        when(mockRecord.get("donationHead", String.class)).thenReturn("Test Head");

        // When
        DonationReceiptResponseDTO result = donationReceiptService.getDonationReceiptById(testReceiptId);

        // Then
        assertNotNull(result);
        assertEquals(testReceiptId, result.getId());
        assertEquals("DR001", result.getReceiptNo());
        assertTrue(result.getIsActive());
        assertEquals("Test Org", result.getOrgName());
        assertEquals("Test Head", result.getDonationHead());
        verify(donationReceiptRepository).fetchOneByIdIncludesOrgNameDonationHead(testReceiptId);
    }

    @Test
    @DisplayName("Should throw exception when getting donation receipt by ID not found")
    void getDonationReceiptById_NotFound_ThrowsException() {
        // Given
        when(donationReceiptRepository.fetchOneByIdIncludesOrgNameDonationHead(testReceiptId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donationReceiptService.getDonationReceiptById(testReceiptId);
        });
        assertEquals("DR GET - No Record Found", exception.getMessage());
        verify(donationReceiptRepository).fetchOneByIdIncludesOrgNameDonationHead(testReceiptId);
    }

    @Test
    @DisplayName("Should apply default pagination values when null")
    void fetchAllDonationReceipts_ApplyDefaults() {
        // Given
        UUID individualId = UUID.randomUUID();
        DonationReceiptPaginationRequest request = new DonationReceiptPaginationRequest();
        // Leave page and pageSize as null to test defaults

        List<Record> mockRecords = Arrays.asList(mock(Record.class));
        when(individualRoleRepository.applyOrganizationFilterDonationReceipt(eq(individualId), any(Condition.class)))
                .thenReturn(mock(Condition.class));
        when(donationReceiptRepository.fetchDonationReceiptByPagination(any(), any()))
                .thenReturn(mockRecords);
        when(donationReceiptRepository.fetchDonationReceiptCount(any())).thenReturn(1);

        // When
        GetAllDonationReceipts result = donationReceiptService.fetchAllDonationReceipts(request, individualId);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), request.getPage()); // Default applied
        assertEquals(Integer.valueOf(10), request.getPageSize()); // Default applied
        verify(donationReceiptRepository).fetchDonationReceiptByPagination(eq(request), any(Condition.class));
    }

    @Test
    @DisplayName("Should generate receipt PDF successfully")
    void generateReceipt_Success() throws IOException, MessagingException {
        // Given
        byte[] mockPdfBytes = "mock pdf content".getBytes();

        // Mock the private method getByReceiptNo through reflection or by mocking its dependencies
        when(donationReceiptRepository.getById(testReceiptId)).thenReturn(testReceipt);
        when(donationHeadRepository.fetchOneById(any())).thenReturn(createMockDonationHead());
        when(organizationRepository.getById(any())).thenReturn(createMockOrganisation());
        when(individualRoleRepository.getTenantAdminOfOrganisation(any())).thenReturn(Arrays.asList(createMockIndividualRole()));
        when(individualRepository.getById(any())).thenReturn(createMockIndividual());
        when(donorsRepository.getByDonorId(any())).thenReturn(createMockDonor());
        when(pdfGenerateService.generatePdfFile(eq(RECEIPT_TEMPLATE), any(Context.class), eq("receipt.pdf")))
                .thenReturn(mockPdfBytes);

        // When
        byte[] result = donationReceiptService.generateReceipt(testReceiptId);

        // Then
        assertNotNull(result);
        assertEquals(mockPdfBytes, result);
        verify(pdfGenerateService).generatePdfFile(eq(RECEIPT_TEMPLATE), any(Context.class), eq("receipt.pdf"));
        verify(notificationManager).sendGeneratedReceipt(any(File.class), anyString(), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("Should handle sendReceiptMessage with valid parameters")
    void sendReceiptMessage_Success() {
        // Given
        String mobileNumber = "9876543210";

        when(donationReceiptRepository.getById(testReceiptId)).thenReturn(testReceipt);
        when(donationHeadRepository.fetchOneById(any())).thenReturn(createMockDonationHead());
        when(organizationRepository.getById(any())).thenReturn(createMockOrganisation());
        when(individualRoleRepository.getTenantAdminOfOrganisation(any())).thenReturn(Arrays.asList(createMockIndividualRole()));
        when(individualRepository.getById(any())).thenReturn(createMockIndividual());
        when(donorsRepository.getByDonorId(any())).thenReturn(createMockDonor());
        when(pdfGenerateService.generatePdfFile(any(), any(), any())).thenReturn("mock pdf".getBytes());

        // When & Then - This method involves Google Drive integration which is complex to mock
        // For now, we'll test the validation logic
        assertThrows(RuntimeException.class, () -> {
            donationReceiptService.sendReceiptMessage(mobileNumber, testReceiptId);
        });
    }

    @Test
    @DisplayName("Should throw exception for blank mobile number in sendReceiptMessage")
    void sendReceiptMessage_BlankMobileNumber_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donationReceiptService.sendReceiptMessage("", testReceiptId);
        });
        assertEquals("Mobile number is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should throw exception for null receipt ID in sendReceiptMessage")
    void sendReceiptMessage_NullReceiptId_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donationReceiptService.sendReceiptMessage("9876543210", null);
        });
        assertEquals("Receipt ID is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should get donation receipts by ID successfully")
    void getDonationReceiptsById_Success() {
        // Given
        when(donationReceiptRepository.getById(testReceiptId)).thenReturn(testReceipt);
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(createMockDonor());

        // When
        DonationReceiptResponseDTO result = donationReceiptService.getDonationReceiptsById(testReceiptId);

        // Then
        assertNotNull(result);
        verify(donationReceiptRepository).getById(testReceiptId);
        verify(donorsRepository).getByDonorId(testDonorId);
    }

    @Test
    @DisplayName("Should throw exception when donation receipt not found in getDonationReceiptsById")
    void getDonationReceiptsById_NotFound_ThrowsException() {
        // Given
        when(donationReceiptRepository.getById(testReceiptId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donationReceiptService.getDonationReceiptsById(testReceiptId);
        });
        assertEquals("Donation Receipt Not found", exception.getMessage());
        verify(donationReceiptRepository).getById(testReceiptId);
    }

    // Helper methods for creating mock objects

    private DonationHeads createMockDonationHead() {
        DonationHeads head = new DonationHeads();
        head.setId(testDonationHeadId);
        head.setName("Test Donation Head");
        return head;
    }

    private Organisation createMockOrganisation() {
        Organisation org = new Organisation();
        org.setId(testOrgId);
        org.setName("Test Organization");
        return org;
    }

    private IndividualRole createMockIndividualRole() {
        IndividualRole role = new IndividualRole();
        role.setIndividualId(UUID.randomUUID());
        return role;
    }

    private Individual createMockIndividual() {
        Individual individual = new Individual();
        individual.setId(UUID.randomUUID());
        individual.setName("Test Individual");
        individual.setEmail("<EMAIL>");
        individual.setMobileNumber("9876543210");
        return individual;
    }

    private Donors createMockDonor() {
        Donors donor = new Donors();
        donor.setId(testDonorId);
        donor.setName("Test Donor");
        donor.setEmail("<EMAIL>");
        return donor;
    }

}