package com.chidhagni.donationreceipt.donationreceipts;

import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptPaginationRequest;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptRequestDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.GetAllDonationReceipts;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import jakarta.mail.MessagingException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@WebMvcTest(DonationReceiptController.class)
class DonationReceiptControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DonationReceiptService donationReceiptService;

    @Autowired
    private ObjectMapper objectMapper;

    private UUID testReceiptId;
    private UUID testUserId;
    private DonationReceiptRequestDTO testRequestDTO;
    private DonationReceiptResponseDTO testResponseDTO;
    private UserPrincipal testUserPrincipal;

    @BeforeEach
    void setUp() {
        testReceiptId = UUID.randomUUID();
        testUserId = UUID.randomUUID();
        
        testRequestDTO = DonationReceiptRequestDTO.builder()
                .donorId(UUID.randomUUID())
                .donationHeadId(UUID.randomUUID())
                .amount(BigDecimal.valueOf(1000))
                .paymentMode("Cash")
                .receiptDate(LocalDateTime.now())
                .build();

        testResponseDTO = DonationReceiptResponseDTO.builder()
                .id(testReceiptId)
                .receiptNo("DR001")
                .donorName("Test Donor")
                .amount(BigDecimal.valueOf(1000))
                .paymentMode("Cash")
                .isActive(true)
                .build();

        testUserPrincipal = UserPrincipal.builder()
                .id(testUserId)
                .email("<EMAIL>")
                .build();
    }

    @Test
    @DisplayName("Should create donation receipt successfully")
    @WithMockUser
    void createDonationReceipt_Success() throws Exception {
        // Given
        when(donationReceiptService.createDonationReceipt(any(DonationReceiptRequestDTO.class), eq(testUserId)))
                .thenReturn(testReceiptId);

        // When & Then
        mockMvc.perform(post("/donation-receipts")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isCreated())
                .andExpect(content().string("\"" + testReceiptId + "\""));

        verify(donationReceiptService).createDonationReceipt(any(DonationReceiptRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should return bad request for invalid donation receipt data")
    @WithMockUser
    void createDonationReceipt_InvalidData_BadRequest() throws Exception {
        // Given - Invalid request with null required fields
        DonationReceiptRequestDTO invalidRequest = DonationReceiptRequestDTO.builder().build();

        // When & Then
        mockMvc.perform(post("/donation-receipts")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        verify(donationReceiptService, never()).createDonationReceipt(any(), any());
    }

    @Test
    @DisplayName("Should update donation receipt successfully")
    @WithMockUser
    void updateDonationReceipt_Success() throws Exception {
        // Given
        doNothing().when(donationReceiptService)
                .updateDonationReceipt(eq(testReceiptId), any(DonationReceiptRequestDTO.class), eq(testUserId));

        // When & Then
        mockMvc.perform(put("/donation-receipts/{id}", testReceiptId)
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isOk());

        verify(donationReceiptService).updateDonationReceipt(eq(testReceiptId), any(DonationReceiptRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should activate donation receipt successfully")
    @WithMockUser
    void activateDonationReceipt_Success() throws Exception {
        // Given
        doNothing().when(donationReceiptService).activateDonationReceipt(testReceiptId, testUserId);

        // When & Then
        mockMvc.perform(patch("/donation-receipts/{id}", testReceiptId)
                        .with(user(testUserPrincipal)))
                .andExpect(status().isOk());

        verify(donationReceiptService).activateDonationReceipt(testReceiptId, testUserId);
    }

    @Test
    @DisplayName("Should deactivate donation receipt successfully")
    @WithMockUser
    void deactivateDonationReceipt_Success() throws Exception {
        // Given
        doNothing().when(donationReceiptService).deactivateDonationReceipt(testReceiptId, testUserId);

        // When & Then
        mockMvc.perform(delete("/donation-receipts/{id}", testReceiptId)
                        .with(user(testUserPrincipal)))
                .andExpect(status().isOk());

        verify(donationReceiptService).deactivateDonationReceipt(testReceiptId, testUserId);
    }

    @Test
    @DisplayName("Should fetch all donation receipts successfully")
    @WithMockUser
    void fetchAllDonationReceipts_Success() throws Exception {
        // Given
        DonationReceiptPaginationRequest paginationRequest = DonationReceiptPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .build();
        
        GetAllDonationReceipts mockResponse = GetAllDonationReceipts.builder()
                .donationReceipts(Arrays.asList(testResponseDTO))
                .totalCount(1)
                .build();

        when(donationReceiptService.fetchAllDonationReceipts(any(DonationReceiptPaginationRequest.class), eq(testUserId)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/donation-receipts/all")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(paginationRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalCount").value(1))
                .andExpect(jsonPath("$.donationReceipts").isArray())
                .andExpect(jsonPath("$.donationReceipts[0].id").value(testReceiptId.toString()));

        verify(donationReceiptService).fetchAllDonationReceipts(any(DonationReceiptPaginationRequest.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should fetch all donation receipts with null pagination request")
    @WithMockUser
    void fetchAllDonationReceipts_NullPaginationRequest_Success() throws Exception {
        // Given
        GetAllDonationReceipts mockResponse = GetAllDonationReceipts.builder()
                .donationReceipts(Arrays.asList(testResponseDTO))
                .totalCount(1)
                .build();

        when(donationReceiptService.fetchAllDonationReceipts(any(DonationReceiptPaginationRequest.class), eq(testUserId)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/donation-receipts/all")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalCount").value(1));

        verify(donationReceiptService).fetchAllDonationReceipts(any(DonationReceiptPaginationRequest.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should get donation receipt by ID successfully")
    @WithMockUser
    void getDonationReceiptById_Success() throws Exception {
        // Given
        when(donationReceiptService.getDonationReceiptsById(testReceiptId)).thenReturn(testResponseDTO);

        // When & Then
        mockMvc.perform(get("/donation-receipts/{id}", testReceiptId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testReceiptId.toString()))
                .andExpect(jsonPath("$.receiptNo").value("DR001"))
                .andExpect(jsonPath("$.donorName").value("Test Donor"))
                .andExpect(jsonPath("$.amount").value(1000))
                .andExpect(jsonPath("$.paymentMode").value("Cash"))
                .andExpect(jsonPath("$.isActive").value(true));

        verify(donationReceiptService).getDonationReceiptsById(testReceiptId);
    }

    @Test
    @DisplayName("Should download receipt PDF successfully")
    @WithMockUser
    void downloadReceipt_Success() throws Exception {
        // Given
        byte[] mockPdfBytes = "mock pdf content".getBytes();
        when(donationReceiptService.generateReceipt(testReceiptId)).thenReturn(mockPdfBytes);

        // When & Then
        mockMvc.perform(post("/donation-receipts/generate-pdf/{id}", testReceiptId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Disposition", "attachment; filename=receipt.pdf"))
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(content().bytes(mockPdfBytes));

        verify(donationReceiptService).generateReceipt(testReceiptId);
    }

    @Test
    @DisplayName("Should return not found when PDF generation fails")
    @WithMockUser
    void downloadReceipt_GenerationFails_NotFound() throws Exception {
        // Given
        when(donationReceiptService.generateReceipt(testReceiptId)).thenReturn(null);

        // When & Then
        mockMvc.perform(post("/donation-receipts/generate-pdf/{id}", testReceiptId))
                .andExpect(status().isNotFound());

        verify(donationReceiptService).generateReceipt(testReceiptId);
    }

    @Test
    @DisplayName("Should handle IOException during PDF generation")
    @WithMockUser
    void downloadReceipt_IOException_InternalServerError() throws Exception {
        // Given
        when(donationReceiptService.generateReceipt(testReceiptId))
                .thenThrow(new IOException("PDF generation failed"));

        // When & Then
        mockMvc.perform(post("/donation-receipts/generate-pdf/{id}", testReceiptId))
                .andExpect(status().isInternalServerError());

        verify(donationReceiptService).generateReceipt(testReceiptId);
    }

    @Test
    @DisplayName("Should handle MessagingException during PDF generation")
    @WithMockUser
    void downloadReceipt_MessagingException_InternalServerError() throws Exception {
        // Given
        when(donationReceiptService.generateReceipt(testReceiptId))
                .thenThrow(new MessagingException("Email sending failed"));

        // When & Then
        mockMvc.perform(post("/donation-receipts/generate-pdf/{id}", testReceiptId))
                .andExpect(status().isInternalServerError());

        verify(donationReceiptService).generateReceipt(testReceiptId);
    }

    @Test
    @DisplayName("Should send receipt via WhatsApp successfully")
    @WithMockUser
    void sendReceipt_Success() throws Exception {
        // Given
        String mobileNumber = "9876543210";
        when(donationReceiptService.sendReceiptMessage(mobileNumber, testReceiptId)).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/donation-receipts/api/wati/shareReceiptViaWA")
                        .param("mobileNumber", mobileNumber)
                        .param("receiptId", testReceiptId.toString()))
                .andExpect(status().isOk())
                .andExpect(content().string("Message sent successfully"));

        verify(donationReceiptService).sendReceiptMessage(mobileNumber, testReceiptId);
    }

    @Test
    @DisplayName("Should return bad request for empty mobile number")
    @WithMockUser
    void sendReceipt_EmptyMobileNumber_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(post("/donation-receipts/api/wati/shareReceiptViaWA")
                        .param("mobileNumber", "")
                        .param("receiptId", testReceiptId.toString()))
                .andExpect(status().isBadRequest())
                .andExpect(content().string("Mobile number is required"));

        verify(donationReceiptService, never()).sendReceiptMessage(anyString(), any(UUID.class));
    }

    @Test
    @DisplayName("Should return bad request for null mobile number")
    @WithMockUser
    void sendReceipt_NullMobileNumber_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(post("/donation-receipts/api/wati/shareReceiptViaWA")
                        .param("receiptId", testReceiptId.toString()))
                .andExpect(status().isBadRequest())
                .andExpect(content().string("Mobile number is required"));

        verify(donationReceiptService, never()).sendReceiptMessage(anyString(), any(UUID.class));
    }

    @Test
    @DisplayName("Should return bad request for null receipt ID")
    @WithMockUser
    void sendReceipt_NullReceiptId_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(post("/donation-receipts/api/wati/shareReceiptViaWA")
                        .param("mobileNumber", "9876543210"))
                .andExpect(status().isBadRequest())
                .andExpect(content().string("Receipt ID is required"));

        verify(donationReceiptService, never()).sendReceiptMessage(anyString(), any(UUID.class));
    }

    @Test
    @DisplayName("Should return internal server error when message sending fails")
    @WithMockUser
    void sendReceipt_SendingFails_InternalServerError() throws Exception {
        // Given
        String mobileNumber = "9876543210";
        when(donationReceiptService.sendReceiptMessage(mobileNumber, testReceiptId)).thenReturn(false);

        // When & Then
        mockMvc.perform(post("/donation-receipts/api/wati/shareReceiptViaWA")
                        .param("mobileNumber", mobileNumber)
                        .param("receiptId", testReceiptId.toString()))
                .andExpect(status().isInternalServerError())
                .andExpect(content().string("Failed to send message"));

        verify(donationReceiptService).sendReceiptMessage(mobileNumber, testReceiptId);
    }

    @Test
    @DisplayName("Should handle exception during message sending")
    @WithMockUser
    void sendReceipt_Exception_InternalServerError() throws Exception {
        // Given
        String mobileNumber = "9876543210";
        when(donationReceiptService.sendReceiptMessage(mobileNumber, testReceiptId))
                .thenThrow(new RuntimeException("Service unavailable"));

        // When & Then
        mockMvc.perform(post("/donation-receipts/api/wati/shareReceiptViaWA")
                        .param("mobileNumber", mobileNumber)
                        .param("receiptId", testReceiptId.toString()))
                .andExpect(status().isInternalServerError())
                .andExpect(content().string("Error: Service unavailable"));

        verify(donationReceiptService).sendReceiptMessage(mobileNumber, testReceiptId);
    }

    @Test
    @DisplayName("Should handle unauthorized access")
    void createDonationReceipt_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/donation-receipts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isUnauthorized());

        verify(donationReceiptService, never()).createDonationReceipt(any(), any());
    }

    @Test
    @DisplayName("Should handle service exception during creation")
    @WithMockUser
    void createDonationReceipt_ServiceException_InternalServerError() throws Exception {
        // Given
        when(donationReceiptService.createDonationReceipt(any(DonationReceiptRequestDTO.class), eq(testUserId)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        mockMvc.perform(post("/donation-receipts")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isInternalServerError());

        verify(donationReceiptService).createDonationReceipt(any(DonationReceiptRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should handle service exception during update")
    @WithMockUser
    void updateDonationReceipt_ServiceException_InternalServerError() throws Exception {
        // Given
        doThrow(new RuntimeException("Record not found"))
                .when(donationReceiptService)
                .updateDonationReceipt(eq(testReceiptId), any(DonationReceiptRequestDTO.class), eq(testUserId));

        // When & Then
        mockMvc.perform(put("/donation-receipts/{id}", testReceiptId)
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testRequestDTO)))
                .andExpect(status().isInternalServerError());

        verify(donationReceiptService).updateDonationReceipt(eq(testReceiptId), any(DonationReceiptRequestDTO.class), eq(testUserId));
    }

    @Test
    @DisplayName("Should handle invalid UUID in path parameter")
    @WithMockUser
    void getDonationReceiptById_InvalidUUID_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/donation-receipts/{id}", "invalid-uuid"))
                .andExpect(status().isBadRequest());

        verify(donationReceiptService, never()).getDonationReceiptsById(any());
    }

    @Test
    @DisplayName("Should handle service exception during get by ID")
    @WithMockUser
    void getDonationReceiptById_ServiceException_InternalServerError() throws Exception {
        // Given
        when(donationReceiptService.getDonationReceiptsById(testReceiptId))
                .thenThrow(new IllegalArgumentException("Donation Receipt Not found"));

        // When & Then
        mockMvc.perform(get("/donation-receipts/{id}", testReceiptId))
                .andExpect(status().isInternalServerError());

        verify(donationReceiptService).getDonationReceiptsById(testReceiptId);
    }
}
