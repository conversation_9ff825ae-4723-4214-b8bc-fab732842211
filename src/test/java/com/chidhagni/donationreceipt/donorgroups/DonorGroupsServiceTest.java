package com.chidhagni.donationreceipt.donorgroups;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.donorgroups.constants.CommunicationModeEnums;
import com.chidhagni.donationreceipt.donorgroups.dto.request.CommunicationRequestDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsPaginationRequest;
import com.chidhagni.donationreceipt.donorgroups.dto.response.DonorResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetAllDonorGroupsResponses;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetEmailTemplatesDto;
import com.chidhagni.donationreceipt.donorgroups.utils.DonorGroupsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.mobileotp.MobileOtpService;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.HelperClassToSendCommunication;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.mail.MessagingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DonorGroupsService Unit Tests")
class DonorGroupsServiceTest {

    @Mock
    private DonorGroupsRepository donorGroupsRepository;

    @Mock
    private DonorGroupsMapper donorGroupMapper;

    @Mock
    private DonorsRepository donorsRepository;

    @Mock
    private NotificationManager notificationManager;

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private WatiService watiService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private HelperClassToSendCommunication helperClassToSendCommunication;

    @Mock
    private MobileOtpService mobileOtpService;

    @InjectMocks
    private DonorGroupsService donorGroupsService;

    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonorGroupId;
    private UUID testDonorId;
    private DonorGroupsDto testDonorGroupsDto;
    private DonorGroups testDonorGroups;
    private Donors testDonor;
    private UserPrincipal testUserPrincipal;
    private DonorGroupMapping testDonorGroupMapping;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonorGroupId = UUID.randomUUID();
        testDonorId = UUID.randomUUID();

        testUserPrincipal = new UserPrincipal(testUserId);

        testDonorGroupsDto = DonorGroupsDto.builder()
                .name("Test Donor Group")
                .description("Test Description")
                .orgId(testOrgId)
                .donorIds(Arrays.asList(testDonorId))
                .build();

        testDonorGroups = new DonorGroups();
        testDonorGroups.setId(testDonorGroupId);
        testDonorGroups.setName("Test Donor Group");
        testDonorGroups.setDescription("Test Description");
        testDonorGroups.setOrgId(testOrgId);
        testDonorGroups.setIsActive(true);
        testDonorGroups.setCreatedBy(testUserId);
        testDonorGroups.setCreatedOn(LocalDateTime.now());

        testDonor = new Donors();
        testDonor.setId(testDonorId);
        testDonor.setName("Test Donor");
        testDonor.setEmail("<EMAIL>");
        testDonor.setMobileNumber("1234567890");

        testDonorGroupMapping = new DonorGroupMapping();
        testDonorGroupMapping.setId(UUID.randomUUID());
        testDonorGroupMapping.setGroupId(testDonorGroupId);
        testDonorGroupMapping.setDonorId(testDonorId);
        testDonorGroupMapping.setIsActive(true);
    }

    @Test
    @DisplayName("Should create donor group successfully")
    void createDonorGroups_Success() {
        // Given
        when(donorGroupsRepository.existsByName("Test Donor Group", testOrgId))
                .thenReturn(false);
        when(donorGroupMapper.mapToDonorGroups(testDonorGroupsDto, testUserPrincipal))
                .thenReturn(testDonorGroups);
        when(donorGroupsRepository.create(testDonorGroups)).thenReturn(testDonorGroups);
        doNothing().when(donorGroupsRepository).addDonorsToGroup(any(), any(), any());

        // When
        UUID result = donorGroupsService.createDonorGroups(testDonorGroupsDto, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(testDonorGroupId, result);
        verify(donorGroupsRepository).existsByName("Test Donor Group", testOrgId);
        verify(donorGroupMapper).mapToDonorGroups(testDonorGroupsDto, testUserPrincipal);
        verify(donorGroupsRepository).create(testDonorGroups);
        verify(donorGroupsRepository).addDonorsToGroup(testDonorGroupId, Arrays.asList(testDonorId), testUserPrincipal);
    }

    @Test
    @DisplayName("Should throw exception when donor group already exists")
    void createDonorGroups_AlreadyExists_ThrowsException() {
        // Given
        when(donorGroupsRepository.existsByName("Test Donor Group", testOrgId))
                .thenReturn(true);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.createDonorGroups(testDonorGroupsDto, testUserPrincipal);
        });
        verify(donorGroupsRepository).existsByName("Test Donor Group", testOrgId);
        verify(donorGroupsRepository, never()).create(any());
    }

    @Test
    @DisplayName("Should throw exception when donor group creation fails")
    void createDonorGroups_CreationFails_ThrowsException() {
        // Given
        when(donorGroupsRepository.existsByName("Test Donor Group", testOrgId))
                .thenReturn(false);
        when(donorGroupMapper.mapToDonorGroups(testDonorGroupsDto, testUserPrincipal))
                .thenReturn(testDonorGroups);
        when(donorGroupsRepository.create(testDonorGroups)).thenReturn(null);

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            donorGroupsService.createDonorGroups(testDonorGroupsDto, testUserPrincipal);
        });
        verify(donorGroupsRepository).create(testDonorGroups);
        verify(donorGroupsRepository, never()).addDonorsToGroup(any(), any(), any());
    }

    @Test
    @DisplayName("Should get donor group by ID successfully")
    void getDonorGroupsById_Success() {
        // Given
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        when(donorGroupsRepository.getDonorGroupMappingsByGroupId(testDonorGroupId))
                .thenReturn(Arrays.asList(testDonorGroupMapping));
        when(donorGroupsRepository.fetchOneByDonorIdAndGroupId(testDonorId, testDonorGroupId))
                .thenReturn(testDonorGroupMapping);
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);

        DonorResponse mockDonorResponse = DonorResponse.builder()
                .id(testDonorId)
                .name("Test Donor")
                .build();
        when(donorGroupMapper.mapToDonorResponse(testDonor)).thenReturn(mockDonorResponse);

        GetDonorGroupsResponse mockResponse = GetDonorGroupsResponse.builder()
                .id(testDonorGroupId)
                .name("Test Donor Group")
                .build();
        when(donorGroupMapper.mapToDonorGroupsResponse(testDonorGroups, Arrays.asList(mockDonorResponse)))
                .thenReturn(mockResponse);

        // When
        GetDonorGroupsResponse result = donorGroupsService.getDonorGroupsById(testDonorGroupId);

        // Then
        assertNotNull(result);
        assertEquals(testDonorGroupId, result.getId());
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository).getDonorGroupMappingsByGroupId(testDonorGroupId);
    }

    @Test
    @DisplayName("Should throw exception when donor group not found")
    void getDonorGroupsById_NotFound_ThrowsException() {
        // Given
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.getDonorGroupsById(testDonorGroupId);
        });
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository, never()).getDonorGroupMappingsByGroupId(any());
    }

    @Test
    @DisplayName("Should activate donor group successfully")
    void activateDonorGroups_Success() {
        // Given
        testDonorGroups.setIsActive(false);
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        doNothing().when(donorGroupsRepository).update(testDonorGroups);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donorGroupsService.activateDonorGroups(testDonorGroupId, testUserId);

            // Then
            assertTrue(testDonorGroups.getIsActive());
            assertEquals(testUserId, testDonorGroups.getUpdatedBy());
            assertEquals(mockTime, testDonorGroups.getUpdatedOn());
            verify(donorGroupsRepository).getById(testDonorGroupId);
            verify(donorGroupsRepository).update(testDonorGroups);
        }
    }

    @Test
    @DisplayName("Should deactivate donor group successfully")
    void deactivateDonorGroups_Success() {
        // Given
        testDonorGroups.setIsActive(true);
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        doNothing().when(donorGroupsRepository).update(testDonorGroups);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donorGroupsService.deactivateDonorGroups(testDonorGroupId, testUserId);

            // Then
            assertFalse(testDonorGroups.getIsActive());
            assertEquals(testUserId, testDonorGroups.getUpdatedBy());
            assertEquals(mockTime, testDonorGroups.getUpdatedOn());
            verify(donorGroupsRepository).getById(testDonorGroupId);
            verify(donorGroupsRepository).update(testDonorGroups);
        }
    }

    @Test
    @DisplayName("Should throw exception when activating non-existent donor group")
    void activateDonorGroups_NotFound_ThrowsException() {
        // Given
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.activateDonorGroups(testDonorGroupId, testUserId);
        });
        assertEquals("Donor Group Not found", exception.getMessage());
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository, never()).update(any());
    }

    @Test
    @DisplayName("Should throw exception when deactivating non-existent donor group")
    void deactivateDonorGroups_NotFound_ThrowsException() {
        // Given
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.deactivateDonorGroups(testDonorGroupId, testUserId);
        });
        assertEquals("Donor Group Not found", exception.getMessage());
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository, never()).update(any());
    }

    @Test
    @DisplayName("Should get all donor groups successfully")
    void getAllDonorGroupsResponses_Success() {
        // Given
        DonorGroupsPaginationRequest paginationRequest = DonorGroupsPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .build();

        List<GetDonorGroupsResponse> mockResponses = Arrays.asList(
                GetDonorGroupsResponse.builder()
                        .id(testDonorGroupId)
                        .name("Test Donor Group")
                        .build()
        );

        when(donorGroupsRepository.getAllDonorGroupsResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal)))
                .thenReturn(mockResponses);

        // When
        GetAllDonorGroupsResponses result = donorGroupsService.getAllDonorGroupsResponses(paginationRequest, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getRowCount());
        assertEquals(1, result.getDonorGroupsResponses().size());
        assertEquals(testDonorGroupId, result.getDonorGroupsResponses().get(0).getId());
        verify(donorGroupsRepository).getAllDonorGroupsResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal));
    }

    @Test
    @DisplayName("Should apply default pagination values when null")
    void getAllDonorGroupsResponses_ApplyDefaults() {
        // Given
        DonorGroupsPaginationRequest paginationRequest = DonorGroupsPaginationRequest.builder().build();
        // Leave page and pageSize as null to test defaults

        List<GetDonorGroupsResponse> mockResponses = Arrays.asList();
        when(donorGroupsRepository.getAllDonorGroupsResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal)))
                .thenReturn(mockResponses);

        // When
        GetAllDonorGroupsResponses result = donorGroupsService.getAllDonorGroupsResponses(paginationRequest, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), paginationRequest.getPage()); // Default applied
        assertEquals(Integer.valueOf(10), paginationRequest.getPageSize()); // Default applied
        verify(donorGroupsRepository).getAllDonorGroupsResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal));
    }

    @Test
    @DisplayName("Should send communication to donor group successfully")
    void sendCommunicationToDonorInDonorGroup_Success() throws MessagingException {
        // Given
        CommunicationRequestDto communicationRequest = CommunicationRequestDto.builder()
                .communicationModeEnums(CommunicationModeEnums.EMAIL)
                .templateIdOrName("campaign-announcement")
                .build();

        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        when(donorGroupsRepository.getDonorGroupMappingsByGroupId(testDonorGroupId))
                .thenReturn(Arrays.asList(testDonorGroupMapping));
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);
        when(organizationRepository.getById(testOrgId)).thenReturn(createMockOrganisation());
        when(helperClassToSendCommunication.buildEmailContextMap(any(Donors.class), any(Organisation.class)))
                .thenReturn(new HashMap<>());

        // When
        donorGroupsService.sendCommunicationToDonorInDonorGroup(testDonorGroupId, communicationRequest);

        // Then
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository).getDonorGroupMappingsByGroupId(testDonorGroupId);
        verify(donorsRepository).getByDonorId(testDonorId);
        verify(organizationRepository).getById(testOrgId);
        verify(helperClassToSendCommunication).buildEmailContextMap(any(Donors.class), any(Organisation.class));
    }

    @Test
    @DisplayName("Should get email templates successfully")
    void getEmailTemplates_Success() {
        // When
        List<GetEmailTemplatesDto> result = donorGroupsService.getEmailTemplates();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(UUID.fromString("550e8400-e29b-41d4-a716-************"), result.get(0).getId());
        assertEquals("campaign-announcement", result.get(0).getTemplateName());
        assertEquals(UUID.fromString("550e8400-e29b-41d4-a716-************"), result.get(1).getId());
        assertEquals("campaign-thank-you-note", result.get(1).getTemplateName());
    }

    @Test
    @DisplayName("Should throw exception when sending communication to non-existent donor group")
    void sendCommunicationToDonorInDonorGroup_NotFound_ThrowsException() {
        // Given
        CommunicationRequestDto communicationRequest = CommunicationRequestDto.builder()
                .communicationModeEnums(CommunicationModeEnums.EMAIL)
                .templateIdOrName("campaign-announcement")
                .build();

        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.sendCommunicationToDonorInDonorGroup(testDonorGroupId, communicationRequest);
        });
        assertEquals("Donor Group Not found", exception.getMessage());
        verify(donorGroupsRepository).getById(testDonorGroupId);
    }

    // Helper method for creating mock organisation
    private Organisation createMockOrganisation() {
        Organisation org = new Organisation();
        org.setId(testOrgId);
        org.setName("Test Organization");
        // Note: Organisation entity may not have setEmail method, adjust based on actual entity structure
        return org;
    }
}