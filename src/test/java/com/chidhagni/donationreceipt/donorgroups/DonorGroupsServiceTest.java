package com.chidhagni.donationreceipt.donorgroups;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.response.DonorResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetEmailTemplatesDto;
import com.chidhagni.donationreceipt.donorgroups.utils.DonorGroupsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.mobileotp.MobileOtpService;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.HelperClassToSendCommunication;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DonorGroupsService Unit Tests")
class DonorGroupsServiceTest {

    @Mock
    private DonorGroupsRepository donorGroupsRepository;

    @Mock
    private DonorGroupsMapper donorGroupMapper;

    @Mock
    private DonorsRepository donorsRepository;

    @Mock
    private NotificationManager notificationManager;

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private WatiService watiService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private HelperClassToSendCommunication helperClassToSendCommunication;

    @Mock
    private MobileOtpService mobileOtpService;

    @InjectMocks
    private DonorGroupsService donorGroupsService;

    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonorGroupId;
    private UUID testDonorId;
    private DonorGroupsDto testDonorGroupsDto;
    private DonorGroups testDonorGroups;
    private Donors testDonor;
    private UserPrincipal testUserPrincipal;
    private DonorGroupMapping testDonorGroupMapping;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonorGroupId = UUID.randomUUID();
        testDonorId = UUID.randomUUID();

        testUserPrincipal = new UserPrincipal(testUserId);

        testDonorGroupsDto = DonorGroupsDto.builder()
                .name("Test Donor Group")
                .description("Test Description")
                .orgId(testOrgId)
                .donorIds(Arrays.asList(testDonorId))
                .build();

        testDonorGroups = new DonorGroups();
        testDonorGroups.setId(testDonorGroupId);
        testDonorGroups.setName("Test Donor Group");
        testDonorGroups.setDescription("Test Description");
        testDonorGroups.setOrgId(testOrgId);
        testDonorGroups.setIsActive(true);
        testDonorGroups.setCreatedBy(testUserId);
        testDonorGroups.setCreatedOn(LocalDateTime.now());

        testDonor = new Donors();
        testDonor.setId(testDonorId);
        testDonor.setName("Test Donor");
        testDonor.setEmail("<EMAIL>");
        testDonor.setMobileNumber("1234567890");

        testDonorGroupMapping = new DonorGroupMapping();
        testDonorGroupMapping.setId(UUID.randomUUID());
        testDonorGroupMapping.setGroupId(testDonorGroupId);
        testDonorGroupMapping.setDonorId(testDonorId);
        testDonorGroupMapping.setIsActive(true);
    }

    @Test
    @DisplayName("Should create donor group successfully")
    void createDonorGroups_Success() {
        // Given
        when(donorGroupsRepository.existsByName("Test Donor Group", testOrgId))
                .thenReturn(false);
        when(donorGroupMapper.mapToDonorGroups(testDonorGroupsDto, testUserPrincipal))
                .thenReturn(testDonorGroups);
        when(donorGroupsRepository.create(testDonorGroups)).thenReturn(testDonorGroups);
        doNothing().when(donorGroupsRepository).addDonorsToGroup(any(), any(), any());

        // When
        UUID result = donorGroupsService.createDonorGroups(testDonorGroupsDto, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(testDonorGroupId, result);
        verify(donorGroupsRepository).existsByName("Test Donor Group", testOrgId);
        verify(donorGroupMapper).mapToDonorGroups(testDonorGroupsDto, testUserPrincipal);
        verify(donorGroupsRepository).create(testDonorGroups);
        verify(donorGroupsRepository).addDonorsToGroup(testDonorGroupId, Arrays.asList(testDonorId), testUserPrincipal);
    }

    @Test
    @DisplayName("Should throw exception when donor group already exists")
    void createDonorGroups_AlreadyExists_ThrowsException() {
        // Given
        when(donorGroupsRepository.existsByName("Test Donor Group", testOrgId))
                .thenReturn(true);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.createDonorGroups(testDonorGroupsDto, testUserPrincipal);
        });
        verify(donorGroupsRepository).existsByName("Test Donor Group", testOrgId);
        verify(donorGroupsRepository, never()).create(any());
    }

    @Test
    @DisplayName("Should throw exception when donor group creation fails")
    void createDonorGroups_CreationFails_ThrowsException() {
        // Given
        when(donorGroupsRepository.existsByName("Test Donor Group", testOrgId))
                .thenReturn(false);
        when(donorGroupMapper.mapToDonorGroups(testDonorGroupsDto, testUserPrincipal))
                .thenReturn(testDonorGroups);
        when(donorGroupsRepository.create(testDonorGroups)).thenReturn(null);

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            donorGroupsService.createDonorGroups(testDonorGroupsDto, testUserPrincipal);
        });
        verify(donorGroupsRepository).create(testDonorGroups);
        verify(donorGroupsRepository, never()).addDonorsToGroup(any(), any(), any());
    }

    @Test
    @DisplayName("Should get donor group by ID successfully")
    void getDonorGroupsById_Success() {
        // Given
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        when(donorGroupsRepository.getDonorGroupMappingsByGroupId(testDonorGroupId))
                .thenReturn(Arrays.asList(testDonorGroupMapping));
        when(donorGroupsRepository.fetchOneByDonorIdAndGroupId(testDonorId, testDonorGroupId))
                .thenReturn(testDonorGroupMapping);
        when(donorsRepository.getByDonorId(testDonorId)).thenReturn(testDonor);

        DonorResponse mockDonorResponse = DonorResponse.builder()
                .id(testDonorId)
                .name("Test Donor")
                .build();
        when(donorGroupMapper.mapToDonorResponse(testDonor)).thenReturn(mockDonorResponse);

        GetDonorGroupsResponse mockResponse = GetDonorGroupsResponse.builder()
                .id(testDonorGroupId)
                .name("Test Donor Group")
                .build();
        when(donorGroupMapper.mapToDonorGroupsResponse(testDonorGroups, Arrays.asList(mockDonorResponse)))
                .thenReturn(mockResponse);

        // When
        GetDonorGroupsResponse result = donorGroupsService.getDonorGroupsById(testDonorGroupId);

        // Then
        assertNotNull(result);
        assertEquals(testDonorGroupId, result.getId());
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository).getDonorGroupMappingsByGroupId(testDonorGroupId);
    }

    @Test
    @DisplayName("Should throw exception when donor group not found")
    void getDonorGroupsById_NotFound_ThrowsException() {
        // Given
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            donorGroupsService.getDonorGroupsById(testDonorGroupId);
        });
        verify(donorGroupsRepository).getById(testDonorGroupId);
        verify(donorGroupsRepository, never()).getDonorGroupMappingsByGroupId(any());
    }

    @Test
    @DisplayName("Should activate donor group successfully")
    void activateDonorGroups_Success() {
        // Given
        testDonorGroups.setIsActive(false);
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        doNothing().when(donorGroupsRepository).update(testDonorGroups);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donorGroupsService.activateDonorGroups(testDonorGroupId, testUserId);

            // Then
            assertTrue(testDonorGroups.getIsActive());
            assertEquals(testUserId, testDonorGroups.getUpdatedBy());
            assertEquals(mockTime, testDonorGroups.getUpdatedOn());
            verify(donorGroupsRepository).getById(testDonorGroupId);
            verify(donorGroupsRepository).update(testDonorGroups);
        }
    }

    @Test
    @DisplayName("Should deactivate donor group successfully")
    void deactivateDonorGroups_Success() {
        // Given
        testDonorGroups.setIsActive(true);
        when(donorGroupsRepository.getById(testDonorGroupId)).thenReturn(testDonorGroups);
        doNothing().when(donorGroupsRepository).update(testDonorGroups);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            donorGroupsService.deactivateDonorGroups(testDonorGroupId, testUserId);

            // Then
            assertFalse(testDonorGroups.getIsActive());
            assertEquals(testUserId, testDonorGroups.getUpdatedBy());
            assertEquals(mockTime, testDonorGroups.getUpdatedOn());
            verify(donorGroupsRepository).getById(testDonorGroupId);
            verify(donorGroupsRepository).update(testDonorGroups);
        }
    }


} 