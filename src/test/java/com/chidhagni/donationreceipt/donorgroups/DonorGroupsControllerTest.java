package com.chidhagni.donationreceipt.donorgroups;

import com.chidhagni.donationreceipt.donorgroups.dto.request.CommunicationRequestDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsPaginationRequest;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetAllDonorGroupsResponses;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetEmailTemplatesDto;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import jakarta.mail.MessagingException;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@WebMvcTest(DonorGroupsController.class)
class DonorGroupsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DonorGroupsService donorGroupsService;

    @Autowired
    private ObjectMapper objectMapper;

    private UUID testDonorGroupId;
    private UUID testUserId;
    private UUID testOrgId;
    private UUID testDonorId;
    private DonorGroupsDto testDonorGroupsDto;
    private GetDonorGroupsResponse testDonorGroupsResponse;
    private UserPrincipal testUserPrincipal;

    @BeforeEach
    void setUp() {
        testDonorGroupId = UUID.randomUUID();
        testUserId = UUID.randomUUID();
        testOrgId = UUID.randomUUID();
        testDonorId = UUID.randomUUID();

        testDonorGroupsDto = DonorGroupsDto.builder()
                .name("Test Donor Group")
                .description("Test Description")
                .orgId(testOrgId)
                .donorIds(Arrays.asList(testDonorId))
                .build();

        testDonorGroupsResponse = GetDonorGroupsResponse.builder()
                .id(testDonorGroupId)
                .name("Test Donor Group")
                .description("Test Description")
                .orgId(testOrgId)
                .isActive(true)
                .build();

        testUserPrincipal = UserPrincipal.builder()
                .id(testUserId)
                .email("<EMAIL>")
                .build();
    }

    @Test
    @DisplayName("Should create donor group successfully")
    @WithMockUser
    void createDonorGroups_Success() throws Exception {
        // Given
        when(donorGroupsService.createDonorGroups(any(DonorGroupsDto.class), any(UserPrincipal.class)))
                .thenReturn(testDonorGroupId);

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/create-donor-groups")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDonorGroupsDto)))
                .andExpect(status().isCreated())
                .andExpect(content().string("\"" + testDonorGroupId + "\""));

        verify(donorGroupsService).createDonorGroups(any(DonorGroupsDto.class), any(UserPrincipal.class));
    }

    @Test
    @DisplayName("Should return bad request for invalid donor group data")
    @WithMockUser
    void createDonorGroups_InvalidData_BadRequest() throws Exception {
        // Given - Invalid request with null required fields
        DonorGroupsDto invalidRequest = DonorGroupsDto.builder().build();

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/create-donor-groups")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        verify(donorGroupsService, never()).createDonorGroups(any(), any());
    }

    @Test
    @DisplayName("Should handle service exception during creation")
    @WithMockUser
    void createDonorGroups_ServiceException_InternalServerError() throws Exception {
        // Given
        when(donorGroupsService.createDonorGroups(any(DonorGroupsDto.class), any(UserPrincipal.class)))
                .thenThrow(new IllegalArgumentException("Donor Groups Already Exists with the given name"));

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/create-donor-groups")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDonorGroupsDto)))
                .andExpect(status().isInternalServerError());

        verify(donorGroupsService).createDonorGroups(any(DonorGroupsDto.class), any(UserPrincipal.class));
    }

    @Test
    @DisplayName("Should get donor group by ID successfully")
    @WithMockUser
    void getDonorGroupsById_Success() throws Exception {
        // Given
        when(donorGroupsService.getDonorGroupsById(testDonorGroupId)).thenReturn(testDonorGroupsResponse);

        // When & Then
        mockMvc.perform(get("/api/v1/donor-groups/get-by-id/{id}", testDonorGroupId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testDonorGroupId.toString()))
                .andExpect(jsonPath("$.name").value("Test Donor Group"))
                .andExpect(jsonPath("$.description").value("Test Description"))
                .andExpect(jsonPath("$.orgId").value(testOrgId.toString()))
                .andExpect(jsonPath("$.isActive").value(true));

        verify(donorGroupsService).getDonorGroupsById(testDonorGroupId);
    }

    @Test
    @DisplayName("Should return not found when donor group does not exist")
    @WithMockUser
    void getDonorGroupsById_NotFound() throws Exception {
        // Given
        when(donorGroupsService.getDonorGroupsById(testDonorGroupId)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/v1/donor-groups/get-by-id/{id}", testDonorGroupId))
                .andExpect(status().isNotFound());

        verify(donorGroupsService).getDonorGroupsById(testDonorGroupId);
    }

    @Test
    @DisplayName("Should update donor group successfully")
    @WithMockUser
    void updateDonorGroups_Success() throws Exception {
        // Given
        when(donorGroupsService.updateDonorGroups(eq(testDonorGroupId), any(DonorGroupsDto.class), any(UserPrincipal.class)))
                .thenReturn(testDonorGroupsResponse);

        // When & Then
        mockMvc.perform(patch("/api/v1/donor-groups/update/{id}", testDonorGroupId)
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDonorGroupsDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testDonorGroupId.toString()))
                .andExpect(jsonPath("$.name").value("Test Donor Group"));

        verify(donorGroupsService).updateDonorGroups(eq(testDonorGroupId), any(DonorGroupsDto.class), any(UserPrincipal.class));
    }

    @Test
    @DisplayName("Should activate donor group successfully")
    @WithMockUser
    void activateDonorGroups_Success() throws Exception {
        // Given
        doNothing().when(donorGroupsService).activateDonorGroups(testDonorGroupId, testUserId);

        // When & Then
        mockMvc.perform(patch("/api/v1/donor-groups/activate/{id}", testDonorGroupId)
                        .with(user(testUserPrincipal)))
                .andExpect(status().isOk());

        verify(donorGroupsService).activateDonorGroups(testDonorGroupId, testUserId);
    }

    @Test
    @DisplayName("Should deactivate donor group successfully")
    @WithMockUser
    void deactivateDonorGroups_Success() throws Exception {
        // Given
        doNothing().when(donorGroupsService).deactivateDonorGroups(testDonorGroupId, testUserId);

        // When & Then
        mockMvc.perform(delete("/api/v1/donor-groups/deactivate/{id}", testDonorGroupId)
                        .with(user(testUserPrincipal)))
                .andExpect(status().isOk());

        verify(donorGroupsService).deactivateDonorGroups(testDonorGroupId, testUserId);
    }

    @Test
    @DisplayName("Should get all donor groups successfully")
    @WithMockUser
    void getAllDonorGroups_Success() throws Exception {
        // Given
        DonorGroupsPaginationRequest paginationRequest = DonorGroupsPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .build();

        GetAllDonorGroupsResponses mockResponse = GetAllDonorGroupsResponses.builder()
                .donorGroupsResponses(Arrays.asList(testDonorGroupsResponse))
                .rowCount(1)
                .build();

        when(donorGroupsService.getAllDonorGroupsResponses(any(DonorGroupsPaginationRequest.class), any(UserPrincipal.class)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/all")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(paginationRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rowCount").value(1))
                .andExpect(jsonPath("$.donorGroupsResponses").isArray())
                .andExpect(jsonPath("$.donorGroupsResponses[0].id").value(testDonorGroupId.toString()));

        verify(donorGroupsService).getAllDonorGroupsResponses(any(DonorGroupsPaginationRequest.class), any(UserPrincipal.class));
    }

    @Test
    @DisplayName("Should get all donor groups with null pagination request")
    @WithMockUser
    void getAllDonorGroups_NullPaginationRequest_Success() throws Exception {
        // Given
        GetAllDonorGroupsResponses mockResponse = GetAllDonorGroupsResponses.builder()
                .donorGroupsResponses(Arrays.asList(testDonorGroupsResponse))
                .rowCount(1)
                .build();

        when(donorGroupsService.getAllDonorGroupsResponses(any(DonorGroupsPaginationRequest.class), any(UserPrincipal.class)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/all")
                        .with(user(testUserPrincipal))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rowCount").value(1));

        verify(donorGroupsService).getAllDonorGroupsResponses(any(DonorGroupsPaginationRequest.class), any(UserPrincipal.class));
    }

    @Test
    @DisplayName("Should send communication to donor group successfully")
    @WithMockUser
    void sendCommunicationToDonorInDonorGroup_Success() throws Exception {
        // Given
        CommunicationRequestDto communicationRequest = CommunicationRequestDto.builder()
                .subject("Test Subject")
                .message("Test Message")
                .build();

        doNothing().when(donorGroupsService)
                .sendCommunicationToDonorInDonorGroup(eq(testDonorGroupId), any(CommunicationRequestDto.class));

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/send-communication/{donorGroupId}", testDonorGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(communicationRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string("Communication sent successfully"));

        verify(donorGroupsService).sendCommunicationToDonorInDonorGroup(eq(testDonorGroupId), any(CommunicationRequestDto.class));
    }

    @Test
    @DisplayName("Should handle messaging exception during communication")
    @WithMockUser
    void sendCommunicationToDonorInDonorGroup_MessagingException_InternalServerError() throws Exception {
        // Given
        CommunicationRequestDto communicationRequest = CommunicationRequestDto.builder()
                .subject("Test Subject")
                .message("Test Message")
                .build();

        doThrow(new MessagingException("Email service unavailable"))
                .when(donorGroupsService)
                .sendCommunicationToDonorInDonorGroup(eq(testDonorGroupId), any(CommunicationRequestDto.class));

        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/send-communication/{donorGroupId}", testDonorGroupId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(communicationRequest)))
                .andExpect(status().isInternalServerError());

        verify(donorGroupsService).sendCommunicationToDonorInDonorGroup(eq(testDonorGroupId), any(CommunicationRequestDto.class));
    }

    @Test
    @DisplayName("Should get email templates successfully")
    @WithMockUser
    void getEmailTemplates_Success() throws Exception {
        // Given
        List<GetEmailTemplatesDto> mockTemplates = Arrays.asList(
                GetEmailTemplatesDto.builder()
                        .id(UUID.fromString("550e8400-e29b-41d4-a716-************"))
                        .templateName("campaign-announcement")
                        .build(),
                GetEmailTemplatesDto.builder()
                        .id(UUID.fromString("550e8400-e29b-41d4-a716-************"))
                        .templateName("campaign-thank-you-note")
                        .build()
        );

        when(donorGroupsService.getEmailTemplates()).thenReturn(mockTemplates);

        // When & Then
        mockMvc.perform(get("/api/v1/donor-groups/email-templates"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].templateName").value("campaign-announcement"))
                .andExpect(jsonPath("$[1].templateName").value("campaign-thank-you-note"));

        verify(donorGroupsService).getEmailTemplates();
    }

    @Test
    @DisplayName("Should handle unauthorized access")
    void createDonorGroups_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/donor-groups/create-donor-groups")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDonorGroupsDto)))
                .andExpect(status().isUnauthorized());

        verify(donorGroupsService, never()).createDonorGroups(any(), any());
    }

    @Test
    @DisplayName("Should handle invalid UUID in path parameter")
    @WithMockUser
    void getDonorGroupsById_InvalidUUID_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/donor-groups/get-by-id/{id}", "invalid-uuid"))
                .andExpect(status().isBadRequest());

        verify(donorGroupsService, never()).getDonorGroupsById(any());
    }
}
