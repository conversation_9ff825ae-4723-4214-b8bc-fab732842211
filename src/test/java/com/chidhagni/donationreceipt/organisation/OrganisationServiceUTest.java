package com.chidhagni.donationreceipt.organisation;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.organisation.dto.request.OrganisationPaginationRequest;
import com.chidhagni.donationreceipt.organisation.dto.request.OrganisationRequestDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllOrganisationResponses;
import com.chidhagni.donationreceipt.organisation.dto.response.OrganisationResponseDTO;
import com.chidhagni.donationreceipt.organisation.utils.OrganisationMapper;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for OrganisationService
 * Tests business logic in isolation with mocked dependencies
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OrganisationService Unit Tests")
class OrganisationServiceUTest {

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private OrganisationMapper organisationMapper;

    @InjectMocks
    private OrganisationService organisationService;

    private UUID testOrgId;
    private UUID testUserId;
    private OrganisationRequestDTO testRequestDTO;
    private Organisation testOrganisation;
    private OrganisationResponseDTO testResponseDTO;
    private UserPrincipal testUserPrincipal;

    @BeforeEach
    void setUp() {
        testOrgId = UUID.randomUUID();
        testUserId = UUID.randomUUID();

        testRequestDTO = OrganisationRequestDTO.builder()
                .name("Test Organization")
                .description("Test Description")
                .address("Test Address")
                .email("<EMAIL>")
                .phoneNumber("1234567890")
                .build();

        testOrganisation = new Organisation();
        testOrganisation.setId(testOrgId);
        testOrganisation.setName("Test Organization");
        testOrganisation.setDescription("Test Description");
        testOrganisation.setAddress("Test Address");
        testOrganisation.setIsActive(true);
        testOrganisation.setCreatedBy(testUserId);
        testOrganisation.setCreatedOn(LocalDateTime.now());

        testResponseDTO = OrganisationResponseDTO.builder()
                .id(testOrgId)
                .name("Test Organization")
                .description("Test Description")
                .address("Test Address")
                .isActive(true)
                .createdBy(testUserId)
                .createdOn(LocalDateTime.now())
                .build();

        testUserPrincipal = UserPrincipal.builder()
                .id(testUserId)
                .email("<EMAIL>")
                .build();
    }

    @Test
    @DisplayName("Should create organisation successfully")
    void createOrganisation_Success() {
        // Given
        when(organizationRepository.existsByName("Test Organization")).thenReturn(false);
        when(organisationMapper.organisationRequestDtoToOrganisation(eq(testRequestDTO), eq(testUserId), any(UUID.class)))
                .thenReturn(testOrganisation);
        when(organizationRepository.save(testOrganisation)).thenReturn(testOrganisation);

        // When
        UUID result = organisationService.createOrganisation(testRequestDTO, testUserId);

        // Then
        assertNotNull(result);
        assertEquals(testOrgId, result);
        verify(organizationRepository).existsByName("Test Organization");
        verify(organisationMapper).organisationRequestDtoToOrganisation(eq(testRequestDTO), eq(testUserId), any(UUID.class));
        verify(organizationRepository).save(testOrganisation);
    }

    @Test
    @DisplayName("Should throw exception when organisation already exists")
    void createOrganisation_AlreadyExists_ThrowsException() {
        // Given
        when(organizationRepository.existsByName("Test Organization")).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organisationService.createOrganisation(testRequestDTO, testUserId);
        });
        assertEquals("Organisation Already exists with the given name", exception.getMessage());
        verify(organizationRepository).existsByName("Test Organization");
        verify(organizationRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should update organisation successfully")
    void updateOrganisation_Success() {
        // Given
        Organisation updatedOrganisation = new Organisation();
        updatedOrganisation.setId(testOrgId);
        updatedOrganisation.setUpdatedBy(testUserId);

        when(organizationRepository.getById(testOrgId)).thenReturn(testOrganisation);
        when(organisationMapper.updateOrganisationFromDto(testRequestDTO, testOrganisation, testUserId))
                .thenReturn(updatedOrganisation);
        when(organizationRepository.update(updatedOrganisation)).thenReturn(updatedOrganisation);

        // When
        organisationService.updateOrganisation(testOrgId, testRequestDTO, testUserId);

        // Then
        verify(organizationRepository).getById(testOrgId);
        verify(organisationMapper).updateOrganisationFromDto(testRequestDTO, testOrganisation, testUserId);
        verify(organizationRepository).update(updatedOrganisation);
    }

    @Test
    @DisplayName("Should throw exception when updating non-existent organisation")
    void updateOrganisation_NotFound_ThrowsException() {
        // Given
        when(organizationRepository.getById(testOrgId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organisationService.updateOrganisation(testOrgId, testRequestDTO, testUserId);
        });
        assertEquals("Organisation Not found", exception.getMessage());
        verify(organizationRepository).getById(testOrgId);
        verify(organizationRepository, never()).update(any());
    }

    @Test
    @DisplayName("Should activate organisation successfully")
    void activateOrganisation_Success() {
        // Given
        testOrganisation.setIsActive(false);
        when(organizationRepository.getById(testOrgId)).thenReturn(testOrganisation);
        when(organizationRepository.update(testOrganisation)).thenReturn(testOrganisation);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            organisationService.activateOrganisation(testOrgId, testUserId);

            // Then
            assertTrue(testOrganisation.getIsActive());
            assertEquals(testUserId, testOrganisation.getUpdatedBy());
            assertEquals(mockTime, testOrganisation.getUpdatedOn());
            verify(organizationRepository).getById(testOrgId);
            verify(organizationRepository).update(testOrganisation);
        }
    }

    @Test
    @DisplayName("Should deactivate organisation successfully")
    void deactivateOrganisation_Success() {
        // Given
        testOrganisation.setIsActive(true);
        when(organizationRepository.getById(testOrgId)).thenReturn(testOrganisation);
        when(organizationRepository.update(testOrganisation)).thenReturn(testOrganisation);

        try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
            LocalDateTime mockTime = LocalDateTime.now();
            dateUtilsMock.when(DateUtils::currentTimeIST).thenReturn(mockTime);

            // When
            organisationService.deactivateOrganisation(testOrgId, testUserId);

            // Then
            assertFalse(testOrganisation.getIsActive());
            assertEquals(testUserId, testOrganisation.getUpdatedBy());
            assertEquals(mockTime, testOrganisation.getUpdatedOn());
            verify(organizationRepository).getById(testOrgId);
            verify(organizationRepository).update(testOrganisation);
        }
    }

    @Test
    @DisplayName("Should throw exception when activating non-existent organisation")
    void activateOrganisation_NotFound_ThrowsException() {
        // Given
        when(organizationRepository.getById(testOrgId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organisationService.activateOrganisation(testOrgId, testUserId);
        });
        assertEquals("Organisation Not found", exception.getMessage());
        verify(organizationRepository).getById(testOrgId);
        verify(organizationRepository, never()).update(any());
    }

    @Test
    @DisplayName("Should get organisation by ID successfully")
    void getOrganisationById_Success() {
        // Given
        when(organizationRepository.getById(testOrgId)).thenReturn(testOrganisation);
        when(organisationMapper.organisationToOrganisationResponseDto(testOrganisation))
                .thenReturn(testResponseDTO);

        // When
        OrganisationResponseDTO result = organisationService.getOrganisationById(testOrgId);

        // Then
        assertNotNull(result);
        assertEquals(testOrgId, result.getId());
        assertEquals("Test Organization", result.getName());
        verify(organizationRepository).getById(testOrgId);
        verify(organisationMapper).organisationToOrganisationResponseDto(testOrganisation);
    }

    @Test
    @DisplayName("Should throw exception when organisation not found by ID")
    void getOrganisationById_NotFound_ThrowsException() {
        // Given
        when(organizationRepository.getById(testOrgId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organisationService.getOrganisationById(testOrgId);
        });
        assertEquals("Organisation Not found", exception.getMessage());
        verify(organizationRepository).getById(testOrgId);
        verify(organisationMapper, never()).organisationToOrganisationResponseDto(any());
    }

    @Test
    @DisplayName("Should get all organisations successfully")
    void getAllOrganisationResponses_Success() {
        // Given
        OrganisationPaginationRequest paginationRequest = OrganisationPaginationRequest.builder()
                .page(1)
                .pageSize(10)
                .build();

        List<OrganisationResponseDTO> mockResponses = Arrays.asList(testResponseDTO);
        when(organizationRepository.getAllOrganisationResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal)))
                .thenReturn(mockResponses);

        // When
        GetAllOrganisationResponses result = organisationService.getAllOrganisationResponses(paginationRequest, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getRowCount());
        assertEquals(1, result.getOrganisationResponses().size());
        assertEquals(testOrgId, result.getOrganisationResponses().get(0).getId());
        verify(organizationRepository).getAllOrganisationResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal));
    }

    @Test
    @DisplayName("Should apply default pagination values when null")
    void getAllOrganisationResponses_ApplyDefaults() {
        // Given
        OrganisationPaginationRequest paginationRequest = OrganisationPaginationRequest.builder().build();
        // Leave page and pageSize as null to test defaults

        List<OrganisationResponseDTO> mockResponses = Arrays.asList();
        when(organizationRepository.getAllOrganisationResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal)))
                .thenReturn(mockResponses);

        // When
        GetAllOrganisationResponses result = organisationService.getAllOrganisationResponses(paginationRequest, testUserPrincipal);

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), paginationRequest.getPage()); // Default applied
        assertEquals(Integer.valueOf(10), paginationRequest.getPageSize()); // Default applied
        verify(organizationRepository).getAllOrganisationResponses(any(), any(), eq(paginationRequest), eq(testUserPrincipal));
    }

    @Test
    @DisplayName("Should handle repository exception during creation")
    void createOrganisation_RepositoryException_ThrowsException() {
        // Given
        when(organizationRepository.existsByName("Test Organization")).thenReturn(false);
        when(organisationMapper.organisationRequestDtoToOrganisation(any(), any(), any()))
                .thenReturn(testOrganisation);
        when(organizationRepository.save(testOrganisation))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            organisationService.createOrganisation(testRequestDTO, testUserId);
        });
        assertEquals("Database error", exception.getMessage());
        verify(organizationRepository).save(testOrganisation);
    }

    @Test
    @DisplayName("Should handle repository exception during update")
    void updateOrganisation_RepositoryException_ThrowsException() {
        // Given
        Organisation updatedOrganisation = new Organisation();
        updatedOrganisation.setId(testOrgId);

        when(organizationRepository.getById(testOrgId)).thenReturn(testOrganisation);
        when(organisationMapper.updateOrganisationFromDto(testRequestDTO, testOrganisation, testUserId))
                .thenReturn(updatedOrganisation);
        when(organizationRepository.update(updatedOrganisation))
                .thenThrow(new RuntimeException("Update failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            organisationService.updateOrganisation(testOrgId, testRequestDTO, testUserId);
        });
        assertEquals("Update failed", exception.getMessage());
        verify(organizationRepository).update(updatedOrganisation);
    }
}
