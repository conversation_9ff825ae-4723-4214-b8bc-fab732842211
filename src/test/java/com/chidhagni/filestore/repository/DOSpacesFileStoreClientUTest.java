package com.chidhagni.filestore.repository;

import org.jooq.exception.DataAccessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DOSpacesFileStoreClient Unit Tests")
class DOSpacesFileStoreClientUTest {

    @Mock
    private S3Client s3Client;

    @InjectMocks
    private DOSpacesFileStoreClient doSpacesFileStoreClient;

    private static final String TEST_BUCKET = "chidhagni-ph";
    private static final String TEST_LOCATION = "documents/test";
    private static final String TEST_FILENAME = "test-file.pdf";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(doSpacesFileStoreClient, "bucketName", TEST_BUCKET);
    }


    @Test
    @DisplayName("Should throw exception when bucket does not exist")
    void storeFile_BucketNotExists() {
        // Given
        Path testPath = Paths.get(TEST_FILENAME);
        when(s3Client.headBucket(any(HeadBucketRequest.class)))
                .thenThrow(NoSuchBucketException.builder().build());

        // When & Then
        assertThrows(DataAccessException.class, () ->
                doSpacesFileStoreClient.storeFile(TEST_LOCATION, testPath));

        verify(s3Client).headBucket(any(HeadBucketRequest.class));
        verify(s3Client, never()).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }



    @Test
    @DisplayName("Should throw exception when file not found")
    void getFile_FileNotFound() {
        // Given
        String objectKey = "documents/test/nonexistent.pdf";
        when(s3Client.getObject(any(GetObjectRequest.class), any(Path.class)))
                .thenThrow(NoSuchKeyException.builder().build());

        // When & Then
        assertThrows(DataAccessException.class, () ->
                doSpacesFileStoreClient.getFile(objectKey));

        verify(s3Client).getObject(any(GetObjectRequest.class), any(Path.class));
    }

    @Test
    @DisplayName("Should delete file successfully")
    void deleteFile_Success() {
        // Given
        String objectKey = "documents/test/file.pdf";
        DeleteObjectResponse deleteObjectResponse = DeleteObjectResponse.builder().build();
        when(s3Client.deleteObject(any(DeleteObjectRequest.class)))
                .thenReturn(deleteObjectResponse);

        // When
        assertDoesNotThrow(() -> doSpacesFileStoreClient.deleteFile(objectKey));

        // Then
        verify(s3Client).deleteObject(any(DeleteObjectRequest.class));
    }

    @Test
    @DisplayName("Should return true when file exists")
    void checkIfFileExists_FileExists() {
        // Given
        String objectKey = "documents/test/file.pdf";
        HeadObjectResponse headObjectResponse = HeadObjectResponse.builder().build();
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenReturn(headObjectResponse);

        // When
        boolean result = doSpacesFileStoreClient.checkIfFileExists(objectKey);

        // Then
        assertTrue(result);
        verify(s3Client).headObject(any(HeadObjectRequest.class));
    }

    @Test
    @DisplayName("Should return false when file does not exist")
    void checkIfFileExists_FileNotExists() {
        // Given
        String objectKey = "documents/test/nonexistent.pdf";
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(NoSuchKeyException.builder().build());

        // When
        boolean result = doSpacesFileStoreClient.checkIfFileExists(objectKey);

        // Then
        assertFalse(result);
        verify(s3Client).headObject(any(HeadObjectRequest.class));
    }

    @Test
    @DisplayName("Should handle general exception in checkIfFileExists")
    void checkIfFileExists_GeneralException() {
        // Given
        String objectKey = "documents/test/file.pdf";
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(new RuntimeException("General error"));

        // When
        boolean result = doSpacesFileStoreClient.checkIfFileExists(objectKey);

        // Then
        assertFalse(result);
        verify(s3Client).headObject(any(HeadObjectRequest.class));
    }
}
