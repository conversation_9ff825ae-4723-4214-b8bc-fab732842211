package com.chidhagni.filestore;

import com.chidhagni.filestore.repository.IFileStoreClient;
import com.chidhagni.filestore.service.IFileStoreService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for file storage functionality.
 * These tests can be run against both MinIO and DigitalOcean Spaces
 * by setting the appropriate system properties.
 * 
 * To test with DigitalOcean Spaces:
 * -Dfilestore.provider=digitalocean
 * -DDO_SPACES_ACCESS_KEY=your_access_key
 * -DDO_SPACES_SECRET_KEY=your_secret_key
 * -Dtest.integration.filestore=true
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "filestore.bucket.name=test-bucket",
    "spring.liquibase.enabled=false"
})
@EnabledIfSystemProperty(named = "test.integration.filestore", matches = "true")
@DisplayName("File Store Integration Tests")
class FileStoreIntegrationITest {

    @Autowired
    private IFileStoreService fileStoreService;

    @Autowired
    private IFileStoreClient fileStoreClient;

    @Test
    @DisplayName("Should store and retrieve file successfully")
    void storeAndRetrieveFile_Success() throws IOException {
        // Given
        String testContent = "This is a test file content for integration testing.";
        String fileName = "integration-test-" + UUID.randomUUID() + ".txt";
        Path tempFile = createTempFile(fileName, testContent);
        String location = "integration-tests";

        try {
            // When - Store file
            String storedLocation = fileStoreService.storeFile(location, tempFile);
            
            // Then - Verify storage
            assertNotNull(storedLocation);
            assertTrue(storedLocation.contains(fileName));
            
            // When - Check if file exists
            boolean exists = fileStoreService.checkIfFileExists(storedLocation);
            
            // Then - Verify existence
            assertTrue(exists);
            
            // When - Retrieve file
            File retrievedFile = fileStoreService.getFile(storedLocation);
            
            // Then - Verify retrieval
            assertNotNull(retrievedFile);
            assertTrue(retrievedFile.exists());
            
            String retrievedContent = Files.readString(retrievedFile.toPath());
            assertEquals(testContent, retrievedContent);
            
            // When - Get file content as base64
            String base64Content = fileStoreService.getFileContent(storedLocation);
            
            // Then - Verify base64 content
            assertNotNull(base64Content);
            assertFalse(base64Content.isEmpty());
            
            // Cleanup - Delete file
            fileStoreService.deleteFile(storedLocation);
            
            // Verify deletion
            boolean existsAfterDeletion = fileStoreService.checkIfFileExists(storedLocation);
            assertFalse(existsAfterDeletion);
            
        } finally {
            // Cleanup temp file
            Files.deleteIfExists(tempFile);
        }
    }

    @Test
    @DisplayName("Should handle non-existent file gracefully")
    void getNonExistentFile_ShouldHandleGracefully() {
        // Given
        String nonExistentLocation = "non-existent/file-" + UUID.randomUUID() + ".txt";

        // When & Then
        boolean exists = fileStoreService.checkIfFileExists(nonExistentLocation);
        assertFalse(exists);
    }

    @Test
    @DisplayName("Should store multiple files in different locations")
    void storeMultipleFiles_Success() throws IOException {
        // Given
        String baseLocation = "integration-tests/multiple";
        String[] fileNames = {"file1.txt", "file2.txt", "file3.txt"};
        String[] storedLocations = new String[fileNames.length];

        try {
            // When - Store multiple files
            for (int i = 0; i < fileNames.length; i++) {
                String content = "Content for " + fileNames[i];
                Path tempFile = createTempFile(fileNames[i], content);
                storedLocations[i] = fileStoreService.storeFile(baseLocation, tempFile);
                Files.deleteIfExists(tempFile);
            }

            // Then - Verify all files exist
            for (String location : storedLocations) {
                assertTrue(fileStoreService.checkIfFileExists(location));
            }

            // Cleanup - Delete all files
            for (String location : storedLocations) {
                fileStoreService.deleteFile(location);
            }

            // Verify all files are deleted
            for (String location : storedLocations) {
                assertFalse(fileStoreService.checkIfFileExists(location));
            }

        } catch (Exception e) {
            // Cleanup in case of failure
            for (String location : storedLocations) {
                if (location != null) {
                    try {
                        fileStoreService.deleteFile(location);
                    } catch (Exception cleanupException) {
                        // Ignore cleanup exceptions
                    }
                }
            }
            throw e;
        }
    }

    private Path createTempFile(String fileName, String content) throws IOException {
        Path tempFile = Paths.get(System.getProperty("java.io.tmpdir"), fileName);
        Files.write(tempFile, content.getBytes());
        return tempFile;
    }
}
