package com.chidhagni.test;

import com.chidhagni.test.config.IntegrationTestConfig;
import org.jooq.DSLContext;
import org.jooq.Table;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

@Testcontainers
@SpringBootTest
@ActiveProfiles("integration-test")
@Import(IntegrationTestConfig.class)
@Transactional
public abstract class BaseIntegrationTest {

    @Container
    public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15.3")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(java.time.Duration.ofMinutes(5))
            .withConnectTimeoutSeconds(60)
            .withReuse(true)
            .withCommand("postgres", "-c", "fsync=off", "-c", "synchronous_commit=off", "-c", "max_connections=200", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "log_statement=all", "-c", "log_min_duration_statement=0");

    @Autowired
    private DSLContext context;

    protected abstract Table[] getTable();

    @BeforeAll
    static void beforeAll() {
        System.out.println("=== BaseIntegrationTest: Starting PostgreSQL container ===");
        if (!postgres.isRunning()) {
            postgres.start();
        }
        System.out.println("=== BaseIntegrationTest: PostgreSQL container started ===");
        System.out.println("Container JDBC URL: " + postgres.getJdbcUrl());
        System.out.println("Container Username: " + postgres.getUsername());
        System.out.println("Container Database: " + postgres.getDatabaseName());
        System.out.println("Container Host: " + postgres.getHost());
        System.out.println("Container Port: " + postgres.getMappedPort(5432));
        
        // Wait a bit for the container to be fully ready
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @DynamicPropertySource
    static void overrideProps(DynamicPropertyRegistry registry) {
        System.out.println("=== BaseIntegrationTest: Configuring dynamic properties ===");
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");
        // Enable Liquibase to create database schema during tests
        registry.add("spring.liquibase.enabled", () -> true);
        registry.add("spring.liquibase.change-log", () -> "classpath:db/changelog/db.changelog-master.yaml");
        // Configure HikariCP for better connection management
        registry.add("spring.datasource.hikari.maximum-pool-size", () -> 5);
        registry.add("spring.datasource.hikari.minimum-idle", () -> 1);
        registry.add("spring.datasource.hikari.connection-timeout", () -> 30000);
        registry.add("spring.datasource.hikari.idle-timeout", () -> 600000);
        registry.add("spring.datasource.hikari.max-lifetime", () -> 1800000);
        registry.add("spring.datasource.hikari.leak-detection-threshold", () -> 60000);
        // Disable connection validation for tests
        registry.add("spring.datasource.hikari.validation-timeout", () -> 5000);
        registry.add("spring.datasource.hikari.connection-test-query", () -> "SELECT 1");
        System.out.println("=== BaseIntegrationTest: Dynamic properties configured ===");
    }

    @BeforeEach
    public void setUp() {
        truncateTable(getTable());
    }

    @AfterEach
    public void cleanUp() {
        truncateTable(getTable());
    }

    protected void truncateTable(Table[] tables) {
        for (Table table : tables) {
            context.truncate(table).cascade().execute();
        }
    }
}
