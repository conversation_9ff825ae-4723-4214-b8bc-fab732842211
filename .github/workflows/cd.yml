
  name: CD Pipeline - Donation-Receipt-Backend

  # This pipeline runs the complete CI/CD flow on pushes to main branch:
  # build/test, sonaranalysis, semgrep, docker build and push, and gitops argocd deployment.
  # This workflow is triggered on pushes to main branch.

  on:
    push:
      branches:
        - main

  jobs:
    build-test:
      name: Build and Test
      runs-on: [self-hosted, linux]
      services:
        docker:
          image: docker:24.0-dind
          options: --privileged --volume /var/run/docker.sock:/var/run/docker.sock
      env:
        TESTCONTAINERS_DOCKER_CLIENT_STRATEGY: org.testcontainers.dockerclient.UnixSocketClientProviderStrategy
        TESTCONTAINERS_RYUK_DISABLED: true
        TESTCONTAINERS_REUSE_ENABLE: true
        DOCKER_HOST: unix:///var/run/docker.sock
        TESTCONTAINERS_HOST_OVERRIDE: localhost
      steps:
        - uses: actions/checkout@v3
        - name: Set up JDK 21
          uses: actions/setup-java@v3
          with:
            java-version: '21'
            distribution: 'temurin'
        - name: Make gradlew executable
          run: chmod +x ./gradlew
        - name: Check Docker Status
          run: |
            echo "=== Docker Status ==="
            docker info
            echo "=== Docker Version ==="
            docker version
            echo "=== Docker Socket Permissions ==="
            ls -la /var/run/docker.sock
            echo "=== Docker Images ==="
            docker images
            echo "=== Docker Containers ==="
            docker ps -a
            echo "=== Environment Variables ==="
            env | grep -E "(DOCKER|TESTCONTAINERS)" || echo "No Docker/Testcontainers env vars found"
        - name: Clean up existing containers and test Docker
          run: |
            echo "=== Cleaning up existing containers ==="
            docker container prune -f || echo "No containers to clean"
            docker volume prune -f || echo "No volumes to clean"
            echo "=== Testing Docker Connectivity ==="
            docker run --rm hello-world
            echo "=== Testing PostgreSQL Container ==="
            docker run --rm postgres:15.3 postgres --version
        - name: Cache Gradle dependencies
          uses: actions/cache@v4
          with:
            path: |
              ~/.gradle/caches
              ~/.gradle/wrapper
            key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
            restore-keys: |
              ${{ runner.os }}-gradle-
        - name: Run ALL tests (unit + integration)
          run: |
            echo "=== Starting Test Execution ==="
            echo "=== Pre-test Docker Status ==="
            docker ps -a
            echo "=== Running Tests ==="
            # Run tests with optimized settings for CI
            ./gradlew test --no-daemon --console=plain --info --stacktrace --parallel 2>&1 | tee test-output.log
            TEST_EXIT_CODE=${PIPESTATUS[0]}
            echo "=== Test Execution Complete with exit code: $TEST_EXIT_CODE ==="
            echo "=== Post-test Docker Status ==="
            docker ps -a
            exit $TEST_EXIT_CODE
          timeout-minutes: 25
        - name: Show Test Results Summary
          if: always()
          run: |
            echo "=== Test Results Summary ==="
            if [ -f "build/reports/tests/test/index.html" ]; then
              echo "Test report generated successfully"
            else
              echo "No test report found"
            fi
            echo "=== Gradle Test Exit Code ==="
            ./gradlew test --no-daemon --console=plain --dry-run > /dev/null 2>&1 && echo "Tests completed" || echo "Tests failed"
        - name: Show Specific Test Failures
          if: failure()
          run: |
            echo "=== SPECIFIC TEST FAILURES ==="
            echo "Looking for failed tests in logs..."
            # Show test results from XML files if they exist
            if [ -d "build/test-results/test" ]; then
              echo "=== Test Results from XML ==="
              find build/test-results/test -name "*.xml" -exec echo "=== {} ===" \; -exec cat {} \;
            fi
            # Show summary from test report
            if [ -f "build/reports/tests/test/index.html" ]; then
              echo "=== Test Report Summary ==="
              grep -A 10 -B 5 "FAILED\|ERROR\|Exception" build/reports/tests/test/index.html || echo "No failures found in HTML report"
            fi
            # Show filtered test output with more context
            echo "=== Filtered Test Output ==="
            if [ -f "test-output.log" ]; then
              echo "=== Database Related Errors ==="
              grep -A 5 -B 5 "relation.*does not exist\|PSQLException\|BadSqlGrammarException" test-output.log || echo "No database errors found"
              echo "=== Testcontainers Errors ==="
              grep -A 5 -B 5 "testcontainers\|DockerClientFactory\|Container startup failed" test-output.log || echo "No Testcontainers errors found"
              echo "=== General Test Failures ==="
              grep -E "(FAILED|ERROR|Exception|BUILD FAILED|Tests run:|Failures:|Skipped:|BUILD SUCCESSFUL)" test-output.log || echo "No specific failures found in log"
            fi
            echo "=== End of Test Failures ==="
        - name: Generate JaCoCo XML report
          run: ./gradlew jacocoTestReport
        - name: Upload Jacoco Coverage Report
          uses: actions/upload-artifact@v4
          with:
            name: jacoco-report
            path: build/reports/jacoco/test/jacocoTestReport.xml
        - name: Upload Test Report
          if: always()
          uses: actions/upload-artifact@v4
          with:
            name: junit-test-report
            path: build/reports/tests/test/
        - name: Upload Test Log
          if: always()
          uses: actions/upload-artifact@v4
          with:
            name: test-output-log
            path: test-output.log
        - name: Debug Docker State (if tests fail)
          if: failure()
          run: |
            echo "=== Debug Docker State ==="
            echo "=== All Containers ==="
            docker ps -a
            echo "=== Container Logs ==="
            for container in $(docker ps -aq); do
              echo "=== Logs for container $container ==="
              docker logs $container 2>&1 || echo "Failed to get logs for $container"
            done
            echo "=== Docker System Info ==="
            docker system df
            docker system events --since 30m --until now || echo "No recent Docker events"
            echo "=== Docker Network Info ==="
            docker network ls
            echo "=== Docker Volume Info ==="
            docker volume ls
    sonarqube:
      name: SonarQube Analysis
      runs-on: [self-hosted, linux]
      needs: build-test
      steps:
        - uses: actions/checkout@v3
        - name: Set up JDK 21
          uses: actions/setup-java@v3
          with:
            java-version: '21'
            distribution: 'temurin'
        - name: Make gradlew executable
          run: chmod +x ./gradlew
        - name: Cache Gradle dependencies
          uses: actions/cache@v4
          with:
            path: |
              ~/.gradle/caches
              ~/.gradle/wrapper
            key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
            restore-keys: |
              ${{ runner.os }}-gradle-
        - name: Download Jacoco Report
          uses: actions/download-artifact@v4
          with:
            name: jacoco-report
            path: build/reports/jacoco/test
        - name: Analyze with SonarQube
          env:
            SONAR_HOST_URL: http://*************:9000
            SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          run: |
            ./gradlew compileJava test jacocoTestReport
            ./gradlew sonar
    semgrep-analysis:
      name: Semgrep Static Analysis
      runs-on: ubuntu-latest
      needs: build-test
      steps:
        - name: Checkout Code
          uses: actions/checkout@v3
        - name: Install doctl
          uses: digitalocean/action-doctl@v2
          with:
            token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        - name: Install kubectl
          uses: azure/setup-kubectl@v3
          with:
            version: 'latest'
        - name: Fetch Kubeconfig from DOKS
          run: doctl kubernetes cluster kubeconfig save ${{ secrets.KUBERNETES_CLUSTER_ID }}
        - name: Set Kubernetes Context
          run: kubectl config use-context ${{ secrets.KUBERNETES_CONTEXT }}
        - name: Ensure Namespace Exists
          run: kubectl create namespace semgrep || true
        - name: Create GitHub Token Secret
          run: |
            kubectl create secret generic secure-github-token \
              --from-literal=token=${{ secrets.SECURE_GITHUB_TOKEN }} \
              -n semgrep --dry-run=client -o yaml | kubectl apply -f -
        - name: Apply Semgrep Results PVC
          run: kubectl apply -f k8s/semgrep-pvc.yaml
        - name: Delete Previous Semgrep Job (If Exists)
          run: |
            kubectl delete job semgrep-job -n semgrep --ignore-not-found
            # Wait for the job to be fully deleted
            for i in {1..10}; do
              kubectl get job semgrep-job -n semgrep && sleep 2 || break
            done
        - name: Apply Semgrep Job with dynamic branch
          run: |
            BRANCH="${{ github.head_ref || github.ref_name }}"
            sed "s|value: \"main\" # Default, will be overridden by workflow|value: \"$BRANCH\"|" k8s/semgrep-job.yaml | kubectl apply -f -
        - name: Wait for Semgrep Job to Complete
          run: |
            kubectl wait --for=condition=complete job/semgrep-job -n semgrep --timeout=600s
        - name: Create Semgrep Results Reader Pod
          run: |
            kubectl delete pod semgrep-results-reader -n semgrep --ignore-not-found
            # Wait for the pod to be fully deleted
            for i in {1..10}; do
              kubectl get pod semgrep-results-reader -n semgrep && sleep 2 || break
            done
            kubectl apply -f k8s/semgrep-results-reader.yaml
        - name: Wait for Reader Pod Ready
          run: |
            kubectl wait --for=condition=Ready pod/semgrep-results-reader -n semgrep --timeout=120s
        - name: List files in /results in reader pod
          run: |
            kubectl exec -n semgrep semgrep-results-reader -- ls -l /results
        - name: Copy Semgrep Results from PVC
          run: |
            kubectl cp semgrep/semgrep-results-reader:/results/results.json results.json
        - name: Upload Semgrep Results as Artifact
          uses: actions/upload-artifact@v4
          with:
            name: semgrep-results
            path: results.json
            if-no-files-found: warn
            compression-level: 6
            overwrite: false
            include-hidden-files: false
        - name: Install jq (with repository fix)
          run: |
            # Remove problematic git-lfs repository
            sudo rm -f /etc/apt/sources.list.d/github_git-lfs.list
            sudo apt-get update -qq && sudo apt-get install -y jq
        - name: Fail if Semgrep found blocking issues
          run: |
            if jq '.results[] | select(.severity == "ERROR")' results.json | grep -q .; then
              echo "Semgrep found ERROR-level issues! Failing the workflow."
              echo "--- Semgrep Findings (first 100 lines) ---"
              jq '.results[] | select(.severity == "ERROR")' results.json | head -100
              echo "------------------------"
              exit 1
            else
              echo "No ERROR-level Semgrep issues found."
            fi
    docker-build-and-push:
      name: Build and Push Docker Image to DOCR
      runs-on: [self-hosted, linux]
      needs: [sonarqube, semgrep-analysis]
      services:
        docker:
          image: docker:24.0-dind
          options: --privileged
      steps:
        - name: Checkout Code
          uses: actions/checkout@v3
        - name: Install doctl
          uses: digitalocean/action-doctl@v2
          with:
            token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        - name: Authenticate Docker with DigitalOcean Container Registry (DOCR)
          run: doctl registry login
        - name: Set up JDK 21
          uses: actions/setup-java@v3
          with:
            java-version: '21'
            distribution: 'temurin'
        - name: Make gradlew executable
          run: chmod +x ./gradlew
        - name: Build JAR
          run: ./gradlew bootJar
        - name: Build Docker Image
          run: docker build -t donation-receipt-backend:latest .
        - name: Tag Docker Image as Latest for DOCR
          run: |
            IMAGE=registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend:latest
            docker tag donation-receipt-backend:latest $IMAGE
            echo "IMAGE=$IMAGE" >> $GITHUB_ENV
        - name: Push Image to DOCR
          run: |
            docker push $IMAGE
    cd-gitops-deploy:
      name: Trigger GitOps Deployment (Donation-Receipt-Backend)
      runs-on: [self-hosted, linux]
      needs: docker-build-and-push
      steps:
        - name: Prepare Secrets
          id: secrets
          run: |
            SECRETS_JSON=$(cat << EOF | base64 -w 0
            {
              "JWT_SECRET": "${{ secrets.JWT_SECRET }}",
              "ENABLE_DATABASE": true,
              "DB_USER": "${{ secrets.DB_USER }}",
              "DB_SSL_MODE": "require",
              "DB_PASSWORD": "${{ secrets.DB_PASSWORD }}",
              "DB_HOST": "${{ secrets.DB_HOST }}",
              "DB_PORT": "${{ secrets.DB_PORT }}",
              "DB_NAME": "${{ secrets.DB_NAME }}",
              "SMTP_USER": "${{ secrets.SMTP_USER }}",
              "SMTP_PASS": "${{ secrets.SMTP_PASS }}",
              "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID }}",
              "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET }}"
            }
            EOF
            )
            echo "secrets_encoded=$SECRETS_JSON" >> $GITHUB_OUTPUT
        - name: 🚀 Trigger GitOps deployment for Donation-Receipt-Backend
          uses: actions/github-script@v7
          env:
            SECRETS_ENCODED: ${{ steps.secrets.outputs.secrets_encoded }}
          with:
            github-token: ${{ secrets.SECURE_GITHUB_TOKEN }}
            script: |
              console.log('🚀 Triggering GitOps deployment for Donation-Receipt-Backend...');
              console.log('Branch:', '${{ github.ref_name }}');
              console.log('Event:', '${{ github.event_name }}');
              const secretsEncoded = process.env.SECRETS_ENCODED || '';
              const dockerTag = 'latest';
              let environment = 'dev';
              const payload = {
                project_id: 'donation-receipt-backend',
                application_type: 'springboot-backend',
                environment: environment,
                docker_image: 'registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend',
                docker_tag: dockerTag,
                container_port: 8080,
                source_repo: `${context.repo.owner}/${context.repo.repo}`,
                source_branch: '${{ github.ref_name }}',
                commit_sha: context.sha,
                secrets_encoded: secretsEncoded || ''
              };
              console.log('📦 Dispatch payload:', JSON.stringify(payload, null, 2));
              const requiredFields = ['project_id', 'environment', 'docker_image', 'docker_tag'];
              for (const field of requiredFields) {
                if (!payload[field] || payload[field] === '') {
                  throw new Error(`Required field '${field}' is missing or empty`);
                }
              }
              if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
                throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
              }
              if (!['dev', 'staging', 'prod', 'production'].includes(payload.environment)) {
                throw new Error(`Invalid environment: ${payload.environment}. Must be dev, staging, prod, or production.`);
              }
              try {
                await github.rest.repos.createDispatchEvent({
                  owner: 'ChidhagniConsulting',
                  repo: 'gitops-argocd-apps',
                  event_type: 'deploy-to-argocd',
                  client_payload: payload
                });
                console.log(`✅ GitOps deployment triggered successfully!`);
                console.log(`
                🎉 Deployment Summary:
                - Application: ${payload.app_name}
                - Environment: ${payload.environment}
                - Docker Image: ${payload.docker_image}:${payload.docker_tag}
                - Source Branch: ${payload.source_branch}
                - Commit SHA: ${payload.commit_sha}
                `);
              } catch (error) {
                console.error('❌ Failed to trigger GitOps deployment:', error);
                throw error;
              }
